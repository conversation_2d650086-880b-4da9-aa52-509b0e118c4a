<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.xu</groupId>
        <artifactId>divination-modules</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>divination-system</artifactId>

    <description>
        system系统模块
    </description>

    <dependencies>
        <!-- 通用工具-->
        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-translation</artifactId>
        </dependency>

        <!-- OSS功能模块 -->
        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-oss</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-log</artifactId>
        </dependency>

        <!-- excel-->
        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-excel</artifactId>
        </dependency>

        <!-- SMS功能模块 -->
        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-sms</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-tenant</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-idempotent</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-sensitive</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-encrypt</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-websocket</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-sse</artifactId>
        </dependency>

    </dependencies>

</project>
