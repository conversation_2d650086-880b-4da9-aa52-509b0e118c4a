package com.xu.biz.service;


import com.xu.biz.domain.bo.BizAppUpgradeTaskBo;
import com.xu.biz.domain.vo.BizAppUpgradeTaskVo;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 升级计划Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IBizAppUpgradeTaskService {

    /**
     * 查询升级计划
     *
     * @param id 主键
     * @return 升级计划
     */
    BizAppUpgradeTaskVo queryById(Long id);

    /**
     * 分页查询升级计划列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 升级计划分页列表
     */
    TableDataInfo<BizAppUpgradeTaskVo> queryPageList(BizAppUpgradeTaskBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的升级计划列表
     *
     * @param bo 查询条件
     * @return 升级计划列表
     */
    List<BizAppUpgradeTaskVo> queryList(BizAppUpgradeTaskBo bo);

    /**
     * 新增升级计划
     *
     * @param bo 升级计划
     * @return 是否新增成功
     */
    Boolean insertByBo(BizAppUpgradeTaskBo bo);

    /**
     * 修改升级计划
     *
     * @param bo 升级计划
     * @return 是否修改成功
     */
    Boolean updateByBo(BizAppUpgradeTaskBo bo);

    /**
     * 校验并批量删除升级计划信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
