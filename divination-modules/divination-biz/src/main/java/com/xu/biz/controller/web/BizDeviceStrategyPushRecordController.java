package com.xu.biz.controller.web;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.xu.biz.domain.bo.BizDeviceStrategyPushRecordBo;
import com.xu.biz.domain.vo.BizDeviceStrategyPushRecordVo;
import com.xu.biz.service.IBizDeviceStrategyPushRecordService;
import com.xu.common.core.domain.R;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.excel.utils.ExcelUtil;
import com.xu.common.idempotent.annotation.RepeatSubmit;
import com.xu.common.log.annotation.Log;
import com.xu.common.log.enums.BusinessType;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import com.xu.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
/**
 * 媒体推送记录
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/biz/deviceStrategyPushRecord")
public class BizDeviceStrategyPushRecordController extends BaseController {

    private final IBizDeviceStrategyPushRecordService bizDeviceStrategyPushRecordService;

    /**
     * 查询媒体推送记录列表
     */
    @SaCheckPermission("biz:deviceStrategyPushRecord:list")
    @GetMapping("/list")
    public TableDataInfo<BizDeviceStrategyPushRecordVo> list(BizDeviceStrategyPushRecordBo bo, PageQuery pageQuery) {
        return bizDeviceStrategyPushRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出媒体推送记录列表
     */
    @SaCheckPermission("biz:deviceStrategyPushRecord:export")
    @Log(title = "媒体推送记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BizDeviceStrategyPushRecordBo bo, HttpServletResponse response) {
        List<BizDeviceStrategyPushRecordVo> list = bizDeviceStrategyPushRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "媒体推送记录", BizDeviceStrategyPushRecordVo.class, response);
    }

    /**
     * 获取媒体推送记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:deviceStrategyPushRecord:query")
    @GetMapping("/{id}")
    public R<BizDeviceStrategyPushRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(bizDeviceStrategyPushRecordService.queryById(id));
    }

    /**
     * 新增媒体推送记录
     */
    @SaCheckPermission("biz:deviceStrategyPushRecord:add")
    @Log(title = "媒体推送记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BizDeviceStrategyPushRecordBo bo) {
        return toAjax(bizDeviceStrategyPushRecordService.insertByBo(bo));
    }

    /**
     * 修改媒体推送记录
     */
    @SaCheckPermission("biz:deviceStrategyPushRecord:edit")
    @Log(title = "媒体推送记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BizDeviceStrategyPushRecordBo bo) {
        return toAjax(bizDeviceStrategyPushRecordService.updateByBo(bo));
    }

    /**
     * 删除媒体推送记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:deviceStrategyPushRecord:remove")
    @Log(title = "媒体推送记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(bizDeviceStrategyPushRecordService.deleteWithValidByIds(List.of(ids), true));
    }
}
