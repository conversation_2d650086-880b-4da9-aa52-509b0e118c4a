package com.xu.biz.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xu.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 升级计划对象 biz_app_upgrade_task
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_app_upgrade_task")
public class BizAppUpgradeTask extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 安装包ID
     */
    private Long packageId;

    /**
     * 计划推送时间
     */
    private Date planPushTime;

    /**
     * 实际推送时间
     */
    private Date actualPushTime;

    /**
     * 是否定时推送(0否 1是)
     */
    private Long timelyPush;

    /**
     * 是否静默安装(0-否 1-是)
     */
    private Long silentInstall;

    /**
     * 安装模式(IMMEDIATE-立即,IDLE-空闲时,NIGHT-夜间)
     */
    private String installModel;

    /**
     * 删除标识 0否 1是
     */
    @TableLogic
    private Long delFlag;


}
