package com.xu.biz.service;


import com.xu.biz.domain.bo.BizDeviceRemindRelationBo;
import com.xu.biz.domain.vo.BizDeviceRemindRelationVo;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 设备提醒关联Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IBizDeviceRemindRelationService {

    /**
     * 查询设备提醒关联
     *
     * @param deviceId 主键
     * @return 设备提醒关联
     */
    BizDeviceRemindRelationVo queryById(Long deviceId);

    /**
     * 分页查询设备提醒关联列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 设备提醒关联分页列表
     */
    TableDataInfo<BizDeviceRemindRelationVo> queryPageList(BizDeviceRemindRelationBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的设备提醒关联列表
     *
     * @param bo 查询条件
     * @return 设备提醒关联列表
     */
    List<BizDeviceRemindRelationVo> queryList(BizDeviceRemindRelationBo bo);

    /**
     * 新增设备提醒关联
     *
     * @param bo 设备提醒关联
     * @return 是否新增成功
     */
    Boolean insertByBo(BizDeviceRemindRelationBo bo);

    /**
     * 修改设备提醒关联
     *
     * @param bo 设备提醒关联
     * @return 是否修改成功
     */
    Boolean updateByBo(BizDeviceRemindRelationBo bo);

    /**
     * 校验并批量删除设备提醒关联信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
