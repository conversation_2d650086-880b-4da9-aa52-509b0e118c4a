package com.xu.biz.controller.web;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.xu.biz.domain.bo.BizPayConfigBo;
import com.xu.biz.domain.vo.BizPayConfigVo;
import com.xu.biz.service.IBizPayConfigService;
import com.xu.common.core.domain.R;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.excel.utils.ExcelUtil;
import com.xu.common.idempotent.annotation.RepeatSubmit;
import com.xu.common.log.annotation.Log;
import com.xu.common.log.enums.BusinessType;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import com.xu.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 支付配置
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/biz/payConfig")
public class BizPayConfigController extends BaseController {

    private final IBizPayConfigService bizPayConfigService;

    /**
     * 查询支付配置列表
     */
    @SaCheckPermission("biz:payConfig:list")
    @GetMapping("/list")
    public TableDataInfo<BizPayConfigVo> list(BizPayConfigBo bo, PageQuery pageQuery) {
        return bizPayConfigService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出支付配置列表
     */
    @SaCheckPermission("biz:payConfig:export")
    @Log(title = "支付配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BizPayConfigBo bo, HttpServletResponse response) {
        List<BizPayConfigVo> list = bizPayConfigService.queryList(bo);
        ExcelUtil.exportExcel(list, "支付配置", BizPayConfigVo.class, response);
    }

    /**
     * 获取支付配置详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:payConfig:query")
    @GetMapping("/{id}")
    public R<BizPayConfigVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(bizPayConfigService.queryById(id));
    }

    /**
     * 新增支付配置
     */
    @SaCheckPermission("biz:payConfig:add")
    @Log(title = "支付配置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BizPayConfigBo bo) {
        return toAjax(bizPayConfigService.insertByBo(bo));
    }

    /**
     * 修改支付配置
     */
    @SaCheckPermission("biz:payConfig:edit")
    @Log(title = "支付配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BizPayConfigBo bo) {
        return toAjax(bizPayConfigService.updateByBo(bo));
    }

    /**
     * 删除支付配置
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:payConfig:remove")
    @Log(title = "支付配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(bizPayConfigService.deleteWithValidByIds(List.of(ids), true));
    }
}
