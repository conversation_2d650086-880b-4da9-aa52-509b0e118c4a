package com.xu.biz.mq.listener;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.dreamlu.iot.mqtt.codec.MqttQoS;
import net.dreamlu.iot.mqtt.spring.client.MqttClientSubscribe;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2025/06/20
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MqttClientSubscribeListener {

    @MqttClientSubscribe(value = "/qos1/#", qos = MqttQoS.QOS1)
    public void subQos1(String topic, byte[] payload) {
        log.info("topic:{} payload:{}", topic, new String(payload, StandardCharsets.UTF_8));
    }
}
