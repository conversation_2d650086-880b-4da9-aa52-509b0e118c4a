package com.xu.biz.service;

import com.xu.biz.domain.bo.BizDeviceStrategyPushRecordBo;
import com.xu.biz.domain.vo.BizDeviceStrategyPushRecordVo;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 媒体推送记录Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IBizDeviceStrategyPushRecordService {

    /**
     * 查询媒体推送记录
     *
     * @param id 主键
     * @return 媒体推送记录
     */
    BizDeviceStrategyPushRecordVo queryById(Long id);

    /**
     * 分页查询媒体推送记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 媒体推送记录分页列表
     */
    TableDataInfo<BizDeviceStrategyPushRecordVo> queryPageList(BizDeviceStrategyPushRecordBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的媒体推送记录列表
     *
     * @param bo 查询条件
     * @return 媒体推送记录列表
     */
    List<BizDeviceStrategyPushRecordVo> queryList(BizDeviceStrategyPushRecordBo bo);

    /**
     * 新增媒体推送记录
     *
     * @param bo 媒体推送记录
     * @return 是否新增成功
     */
    Boolean insertByBo(BizDeviceStrategyPushRecordBo bo);

    /**
     * 修改媒体推送记录
     *
     * @param bo 媒体推送记录
     * @return 是否修改成功
     */
    Boolean updateByBo(BizDeviceStrategyPushRecordBo bo);

    /**
     * 校验并批量删除媒体推送记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
