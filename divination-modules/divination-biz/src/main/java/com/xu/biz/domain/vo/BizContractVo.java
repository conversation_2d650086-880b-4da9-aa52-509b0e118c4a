package com.xu.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.xu.biz.domain.BizContract;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;



/**
 * 合同管理视图对象 biz_contract
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BizContract.class)
public class BizContractVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String name;

    /**
     * 描述
     */
    @ExcelProperty(value = "描述")
    private String remark;

    /**
     * 租金(元/月)
     */
    @ExcelProperty(value = "租金")
    private BigDecimal rentAmount;

    /**
     * 联系人
     */
    @ExcelProperty(value = "联系人")
    private String contactPerson;

    /**
     * 地址
     */
    @ExcelProperty(value = "地址")
    private String address;

    /**
     * 开始时间
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "结束时间")
    private Date endTime;


}
