package com.xu.biz.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xu.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 维修工单对象 biz_repair_order
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_repair_order")
public class BizRepairOrder extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 维修单ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 维修单编号
     */
    private String orderNo;

    /**
     * 终端设备ID
     */
    private String deviceId;

    /**
     * 终端名称
     */
    private String deviceName;

    /**
     * 故障类型[NO_SIGN:不出签,NO_SYMBOL:不出符,NO_QRCODE:不显示二维码,PAY_FAIL:支付失败,OTHER:其他]
     */
    private String faultType;

    /**
     * 故障详细描述
     */
    private String faultDesc;

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 客户电话
     */
    private String customerPhone;

    /**
     * 客户地址
     */
    private String customerAddress;

    /**
     * 紧急程度(字典)
     */
    private String emergencyLevel;

    /**
     * 报修来源(1-电话报修 2-终端上报 3-平台录入)
     */
    private Integer reportSource;

    /**
     * 维修状态(0-待处理 1-处理中 3-已完成 4-已取消)
     */
    private Integer status;

    /**
     * 维修人员ID
     */
    private Long repairManId;

    /**
     * 维修人员姓名
     */
    private String repairManName;

    /**
     * 维修人员电话
     */
    private String repairManPhone;

    /**
     * 预约上门时间
     */
    private Date appointmentTime;

    /**
     * 维修开始时间
     */
    private Date startTime;

    /**
     * 维修完成时间
     */
    private Date finishTime;

    /**
     * 处理结果
     */
    private String solution;

    /**
     * 客户反馈(1-满意 2-一般 3-不满意)
     */
    private Long customerFeedback;

    /**
     * 删除标识 0否 1是
     */
    @TableLogic
    private Long delFlag;


}
