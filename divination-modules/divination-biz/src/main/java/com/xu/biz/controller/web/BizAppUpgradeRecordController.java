package com.xu.biz.controller.web;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.xu.biz.domain.bo.BizAppUpgradeRecordBo;
import com.xu.biz.domain.vo.BizAppUpgradeRecordVo;
import com.xu.biz.service.IBizAppUpgradeRecordService;
import com.xu.common.core.domain.R;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.excel.utils.ExcelUtil;
import com.xu.common.idempotent.annotation.RepeatSubmit;
import com.xu.common.log.annotation.Log;
import com.xu.common.log.enums.BusinessType;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import com.xu.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * APP推送记录
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/biz/appUpgradeRecord")
public class BizAppUpgradeRecordController extends BaseController {

    private final IBizAppUpgradeRecordService bizAppUpgradeRecordService;

    /**
     * 查询APP推送记录列表
     */
    @SaCheckPermission("biz:appUpgradeRecord:list")
    @GetMapping("/list")
    public TableDataInfo<BizAppUpgradeRecordVo> list(BizAppUpgradeRecordBo bo, PageQuery pageQuery) {
        return bizAppUpgradeRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出APP推送记录列表
     */
    @SaCheckPermission("biz:appUpgradeRecord:export")
    @Log(title = "APP推送记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BizAppUpgradeRecordBo bo, HttpServletResponse response) {
        List<BizAppUpgradeRecordVo> list = bizAppUpgradeRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "APP推送记录", BizAppUpgradeRecordVo.class, response);
    }

    /**
     * 获取APP推送记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:appUpgradeRecord:query")
    @GetMapping("/{id}")
    public R<BizAppUpgradeRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(bizAppUpgradeRecordService.queryById(id));
    }

    /**
     * 新增APP推送记录
     */
    @SaCheckPermission("biz:appUpgradeRecord:add")
    @Log(title = "APP推送记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BizAppUpgradeRecordBo bo) {
        return toAjax(bizAppUpgradeRecordService.insertByBo(bo));
    }

    /**
     * 修改APP推送记录
     */
    @SaCheckPermission("biz:appUpgradeRecord:edit")
    @Log(title = "APP推送记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BizAppUpgradeRecordBo bo) {
        return toAjax(bizAppUpgradeRecordService.updateByBo(bo));
    }

    /**
     * 删除APP推送记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:appUpgradeRecord:remove")
    @Log(title = "APP推送记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(bizAppUpgradeRecordService.deleteWithValidByIds(List.of(ids), true));
    }
}
