package com.xu.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xu.biz.domain.BizSpace;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;



/**
 * 空间管理视图对象 biz_space
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BizSpace.class)
public class BizSpaceVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 空间名称
     */
    @ExcelProperty(value = "空间名称")
    private String name;

    /**
     * 空间编码
     */
    @ExcelProperty(value = "空间编码")
    private String code;

    /**
     * 描述
     */
    @ExcelProperty(value = "描述")
    private String remark;

    /**
     * x坐标
     * @deprecated 此字段已废弃，请使用新的坐标系统
     */
    @Deprecated
    @ExcelProperty(value = "x坐标(已废弃)")
    private String x;

    /**
     * y坐标
     * @deprecated 此字段已废弃，请使用新的坐标系统
     */
    @Deprecated
    @ExcelProperty(value = "y坐标(已废弃)")
    private String y;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序")
    private Integer sort;

    /**
     * 父级ID
     */
    @ExcelProperty(value = "父级ID")
    private Long parentId;


}
