package com.xu.biz.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xu.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 安装包管理对象 biz_app_upgrade_package
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_app_upgrade_package")
public class BizAppUpgradePackage extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 安装包ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 安装包名称
     */
    private String name;

    /**
     * 版本号(整数)
     */
    private Long code;

    /**
     * 版本编号(如1.0.0)
     */
    private String number;

    /**
     * 文件ID
     */
    private String fileId;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 版本说明
     */
    private String releaseNotes;

    /**
     * 是否强制更新(0-否 1-是)
     */
    private Long forceUpdate;

    /**
     * 删除标识 0否 1是
     */
    @TableLogic
    private Long delFlag;


}
