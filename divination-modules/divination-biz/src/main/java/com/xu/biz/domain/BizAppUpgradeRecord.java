package com.xu.biz.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xu.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * APP推送记录对象 biz_app_upgrade_record
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_app_upgrade_record")
public class BizAppUpgradeRecord extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 关联任务ID
     */
    private Long taskId;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 安装包ID
     */
    private Long packageId;

    /**
     * 安装包名称
     */
    private String packageName;

    /**
     * 安装包数字编号
     */
    private Long packageCode;

    /**
     * 升级状态（0已推送 / 1成功 / 2失败）
     */
    private Integer status;

    /**
     * 推送时间
     */
    private Date pushTime;

    /**
     * 删除标识 0否 1是
     */
    @TableLogic
    private Long delFlag;


}
