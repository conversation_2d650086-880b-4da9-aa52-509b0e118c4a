package com.xu.biz.domain.bo;

import com.xu.biz.domain.BizPayMchConfig;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 支付商户管理业务对象 biz_pay_mch_config
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BizPayMchConfig.class, reverseConvertGenerate = false)
public class BizPayMchConfigBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 关联的服务商配置ID
     */
    @NotNull(message = "关联的服务商配置ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long configId;

    /**
     * 子商户号([支付宝]特约商户PID或[微信]特约商户号)
     */
    @NotBlank(message = "子商户号([支付宝]特约商户PID或[微信]特约商户号)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String subMchId;

    /**
     * 子商户应用ID([微信]特约商户APPID，部分场景需要)
     */
    private String subAppId;

    /**
     * 业务类型(用于区分同一商户不同业务)
     */
    private String businessType;

    /**
     * 结算费率
     */
    private Long rate;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态:0-禁用,1-启用
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;


}
