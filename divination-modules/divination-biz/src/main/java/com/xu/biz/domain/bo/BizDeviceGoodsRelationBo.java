package com.xu.biz.domain.bo;

import com.xu.biz.domain.BizDeviceGoodsRelation;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备商品关联业务对象 biz_device_goods_relation
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BizDeviceGoodsRelation.class, reverseConvertGenerate = false)
public class BizDeviceGoodsRelationBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long deviceId;

    /**
     * 商品ID
     */
    @NotNull(message = "商品ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long goodsId;

    /**
     * 数量
     */
    @NotNull(message = "数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long count;


}
