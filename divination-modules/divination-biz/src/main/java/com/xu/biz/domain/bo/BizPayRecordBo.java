package com.xu.biz.domain.bo;

import com.xu.biz.domain.BizPayRecord;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 支付交易记录业务对象 biz_pay_record
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BizPayRecord.class, reverseConvertGenerate = false)
public class BizPayRecordBo extends BaseEntity {

    /**
     * 支付记录ID
     */
    @NotNull(message = "支付记录ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 商户订单号
     */
    @NotBlank(message = "商户订单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String outTradeNo;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 支付配置ID
     */
    @NotNull(message = "支付配置ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long payConfigId;

    /**
     * 商户配置ID
     */
    @NotNull(message = "商户配置ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long mchConfigId;

    /**
     * 商户ID
     */
    @NotBlank(message = "商户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String mchId;

    /**
     * 应用ID
     */
    @NotBlank(message = "应用ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String appId;

    /**
     * 支付渠道(1-支付宝 2-微信)
     */
    @NotNull(message = "支付渠道(1-支付宝 2-微信)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long channel;

    /**
     * 订单金额(元)
     */
    @NotNull(message = "订单金额(元)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long amount;

    /**
     * 实收金额(元)
     */
    private Long actualAmount;

    /**
     * 货币类型
     */
    private String currency;

    /**
     * 返回数据
     */
    private String resultData;

    /**
     * 订单描述
     */
    private String remark;

    /**
     * 状态(0-待支付 1-支付成功 2-已关闭 3-已退款)
     */
    @NotNull(message = "状态(0-待支付 1-支付成功 2-已关闭 3-已退款)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer status;

    /**
     * 付款方用户标识
     */
    private String payerUid;

    /**
     * 付款方账号
     */
    private String payerAccount;

    /**
     * 支付成功时间
     */
    private Date payTime;


}
