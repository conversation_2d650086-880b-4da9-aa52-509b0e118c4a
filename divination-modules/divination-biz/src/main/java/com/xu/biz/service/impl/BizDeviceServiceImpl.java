package com.xu.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xu.biz.domain.BizDevice;
import com.xu.biz.domain.BizDeviceGroup;
import com.xu.biz.domain.BizDeviceType;
import com.xu.biz.domain.bo.BizDeviceBo;
import com.xu.biz.domain.vo.BizDeviceVo;
import com.xu.biz.mapper.BizDeviceGroupMapper;
import com.xu.biz.mapper.BizDeviceMapper;
import com.xu.biz.mapper.BizDeviceTypeMapper;
import com.xu.biz.service.IBizDeviceService;
import com.xu.common.core.constant.MqttConstants;
import com.xu.common.core.utils.MapstructUtils;
import com.xu.common.core.utils.StringUtils;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import com.xu.system.domain.SysArea;
import com.xu.system.mapper.SysAreaMapper;
import lombok.RequiredArgsConstructor;
import net.dreamlu.iot.mqtt.spring.client.MqttClientTemplate;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 设备基础信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RequiredArgsConstructor
@Service
public class BizDeviceServiceImpl implements IBizDeviceService {

    private final BizDeviceMapper baseMapper;
    private final BizDeviceTypeMapper typeMapper;
    private final BizDeviceGroupMapper groupMapper;
    private final SysAreaMapper areaMapper;
    private final MqttClientTemplate mqttClientTemplate;

    /**
     * 查询设备基础信息
     *
     * @param id 主键
     * @return 设备基础信息
     */
    @Override
    public BizDeviceVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询设备基础信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 设备基础信息分页列表
     */
    @Override
    public TableDataInfo<BizDeviceVo> queryPageList(BizDeviceBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BizDevice> lqw = buildQueryWrapper(bo);
        Page<BizDeviceVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        List<BizDeviceVo> list = result.getRecords();
        supple(list);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的设备基础信息列表
     *
     * @param bo 查询条件
     * @return 设备基础信息列表
     */
    @Override
    public List<BizDeviceVo> queryList(BizDeviceBo bo) {
        LambdaQueryWrapper<BizDevice> lqw = buildQueryWrapper(bo);
        List<BizDeviceVo> list = baseMapper.selectVoList(lqw);
        // 补充相应参数
        supple(list);
        return list;
    }

    /**
     * 补充信息
     *
     * @param list
     */
    private void supple(List<BizDeviceVo> list) {
        if (CollUtil.isNotEmpty(list)) {
            // 收集所有需要查询的ID
            List<Long> typeIds = list.parallelStream().map(BizDeviceVo::getTypeId).distinct().collect(Collectors.toList());
            List<Long> groupIds = list.parallelStream().map(BizDeviceVo::getGroupId).distinct().collect(Collectors.toList());
            List<String> areaCodes = list.parallelStream().map(BizDeviceVo::getAreaCode).distinct().collect(Collectors.toList());

            // 批量查询类型名称
            Map<Long, String> typeMap = new HashMap<>();
            if (!typeIds.isEmpty()) {
                List<BizDeviceType> types = typeMapper.selectList(Wrappers.<BizDeviceType>lambdaQuery()
                    .in(BizDeviceType::getId, typeIds));
                typeMap = types.stream().collect(Collectors.toMap(BizDeviceType::getId, BizDeviceType::getName));
            }

            // 批量查询分组名称
            Map<Long, String> groupMap = new HashMap<>();
            if (!groupIds.isEmpty()) {
                List<BizDeviceGroup> groups = groupMapper.selectList(Wrappers.<BizDeviceGroup>lambdaQuery()
                    .in(BizDeviceGroup::getId, groupIds));
                groupMap = groups.stream().collect(Collectors.toMap(BizDeviceGroup::getId, BizDeviceGroup::getName));
            }

            // 批量查询区域名称
            Map<String, String> areaMap = new HashMap<>();
            if (!areaCodes.isEmpty()) {
                List<SysArea> areas = areaMapper.selectList(Wrappers.<SysArea>lambdaQuery()
                    .in(SysArea::getAreaCode, areaCodes));
                areaMap = areas.stream().collect(Collectors.toMap(SysArea::getAreaCode, SysArea::getName));
            }

            // 设置名称
            for (BizDeviceVo vo : list) {
                vo.setTypeName(typeMap.get(vo.getTypeId()));
                vo.setGroupName(groupMap.get(vo.getGroupId()));
                vo.setAreaName(areaMap.get(vo.getAreaCode()));
                if (ObjUtil.isNotNull(vo.getEverBrightTime())) {
                    long totalSeconds = vo.getEverBrightTime();
                    long minutes = totalSeconds / 60;
                    long seconds = totalSeconds % 60;

                    if (seconds == 0) {
                        // 当秒数为0时，只显示分钟
                        vo.setEverBrightTimeStr(minutes + "分");
                    } else {
                        // 当秒数不为0时，显示分钟和秒
                        vo.setEverBrightTimeStr(minutes + "分" + seconds + "秒");
                    }
                }
            }
        }
    }

    private LambdaQueryWrapper<BizDevice> buildQueryWrapper(BizDeviceBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BizDevice> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(BizDevice::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getCode()), BizDevice::getCode, bo.getCode());
        lqw.like(StringUtils.isNotBlank(bo.getName()), BizDevice::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getLocation()), BizDevice::getLocation, bo.getLocation());
        lqw.eq(bo.getAreaCode() != null, BizDevice::getAreaCode, bo.getAreaCode());
        lqw.eq(bo.getTypeId() != null, BizDevice::getTypeId, bo.getTypeId());
        lqw.eq(bo.getGroupId() != null, BizDevice::getGroupId, bo.getGroupId());
        lqw.like(StringUtils.isNotBlank(bo.getContact()), BizDevice::getContact, bo.getContact());
        lqw.like(StringUtils.isNotBlank(bo.getPhone()), BizDevice::getPhone, bo.getPhone());
        lqw.eq(bo.getOpneStatus() != null, BizDevice::getOpneStatus, bo.getOpneStatus());
        lqw.eq(bo.getOnlineStatus() != null, BizDevice::getOnlineStatus, bo.getOnlineStatus());
        lqw.eq(bo.getRepairStatus() != null, BizDevice::getRepairStatus, bo.getRepairStatus());
        lqw.like(bo.getRent() != null, BizDevice::getRent, bo.getRent());
        lqw.eq(bo.getLastHeartbeat() != null, BizDevice::getLastHeartbeat, bo.getLastHeartbeat());
        lqw.eq(StringUtils.isNotBlank(bo.getIp()), BizDevice::getIp, bo.getIp());
        lqw.eq(StringUtils.isNotBlank(bo.getMac()), BizDevice::getMac, bo.getMac());
        lqw.like(StringUtils.isNotBlank(bo.getAppNumber()), BizDevice::getAppNumber, bo.getAppNumber());
        return lqw;
    }

    /**
     * 新增设备基础信息
     *
     * @param bo 设备基础信息
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BizDeviceBo bo) {
        BizDevice add = MapstructUtils.convert(bo, BizDevice.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改设备基础信息
     *
     * @param bo 设备基础信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BizDeviceBo bo) {
        BizDevice update = MapstructUtils.convert(bo, BizDevice.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BizDevice entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除设备基础信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    private void editDevice(BizDevice device) {
        mqttClientTemplate.publish(MqttConstants.CMD_DEVICE_CONFIG,)
    }

    @Override
    public Boolean playStrategy(BizDeviceBo bo) {
        return null;
    }

    @Override
    public Boolean restart(BizDeviceBo bo) {
        return null;
    }

    @Override
    public Boolean forcedShipments(BizDeviceBo bo) {
        return null;
    }
}
