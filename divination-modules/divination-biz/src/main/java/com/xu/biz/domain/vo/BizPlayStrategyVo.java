package com.xu.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.xu.biz.domain.BizPlayStrategy;
import com.xu.common.excel.annotation.ExcelDictFormat;
import com.xu.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * 播放策略视图对象 biz_play_strategy
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BizPlayStrategy.class)
public class BizPlayStrategyVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 策略名称
     */
    @ExcelProperty(value = "策略名称")
    private String name;

    /**
     * 策略描述
     */
    @ExcelProperty(value = "策略描述")
    private String remark;

    /**
     * 播放策略(字典)
     */
    @ExcelDictFormat(dictType = "play_type")
    @ExcelProperty(value = "播放策略",converter = ExcelDictConvert.class)
    private String playStrategy;

    /**
     * 开始时间
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "结束时间")
    private Date endTime;

    /**
     * 播放内容
     */
    private Map<String, List<Long>> mediaConfig;

}
