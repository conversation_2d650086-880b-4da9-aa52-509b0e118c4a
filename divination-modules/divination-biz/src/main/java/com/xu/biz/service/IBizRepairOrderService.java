package com.xu.biz.service;

import com.xu.biz.domain.bo.BizRepairOrderBo;
import com.xu.biz.domain.vo.BizRepairOrderVo;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 维修工单Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IBizRepairOrderService {

    /**
     * 查询维修工单
     *
     * @param id 主键
     * @return 维修工单
     */
    BizRepairOrderVo queryById(Long id);

    /**
     * 分页查询维修工单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 维修工单分页列表
     */
    TableDataInfo<BizRepairOrderVo> queryPageList(BizRepairOrderBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的维修工单列表
     *
     * @param bo 查询条件
     * @return 维修工单列表
     */
    List<BizRepairOrderVo> queryList(BizRepairOrderBo bo);

    /**
     * 新增维修工单
     *
     * @param bo 维修工单
     * @return 是否新增成功
     */
    Boolean insertByBo(BizRepairOrderBo bo);

    /**
     * 修改维修工单
     *
     * @param bo 维修工单
     * @return 是否修改成功
     */
    Boolean updateByBo(BizRepairOrderBo bo);

    /**
     * 校验并批量删除维修工单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
