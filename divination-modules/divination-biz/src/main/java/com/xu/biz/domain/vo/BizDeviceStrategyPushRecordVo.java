package com.xu.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xu.biz.domain.BizDeviceStrategyPushRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 媒体推送记录视图对象 biz_device_strategy_push_record
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BizDeviceStrategyPushRecord.class)
public class BizDeviceStrategyPushRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    private Long id;

    /**
     * 策略ID
     */
    @ExcelProperty(value = "策略ID")
    private Long strategyId;

    /**
     * 策略名称
     */
    @ExcelProperty(value = "策略名称")
    private String strategyName;

    /**
     * 设备ID
     */
    @ExcelProperty(value = "设备ID")
    private Long deviceId;

    /**
     * 设备名称
     */
    @ExcelProperty(value = "设备名称")
    private String deviceName;

    /**
     * 推送内容
     */
    @ExcelProperty(value = "推送内容")
    private String pushContent;

    /**
     * 推送时间
     */
    @ExcelProperty(value = "推送时间")
    private Date pushTime;

    /**
     * 推送批次号
     */
    @ExcelProperty(value = "推送批次号")
    private String pushBatchNo;


}
