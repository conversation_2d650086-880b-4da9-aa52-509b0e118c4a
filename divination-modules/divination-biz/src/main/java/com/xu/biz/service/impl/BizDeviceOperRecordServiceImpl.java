package com.xu.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xu.biz.domain.BizDeviceOperRecord;
import com.xu.biz.domain.bo.BizDeviceOperRecordBo;
import com.xu.biz.domain.vo.BizDeviceOperRecordVo;
import com.xu.biz.mapper.BizDeviceOperRecordMapper;
import com.xu.biz.service.IBizDeviceOperRecordService;
import com.xu.common.core.utils.MapstructUtils;
import com.xu.common.core.utils.StringUtils;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 设备操作记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RequiredArgsConstructor
@Service
public class BizDeviceOperRecordServiceImpl implements IBizDeviceOperRecordService {

    private final BizDeviceOperRecordMapper baseMapper;

    /**
     * 查询设备操作记录
     *
     * @param id 主键
     * @return 设备操作记录
     */
    @Override
    public BizDeviceOperRecordVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询设备操作记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 设备操作记录分页列表
     */
    @Override
    public TableDataInfo<BizDeviceOperRecordVo> queryPageList(BizDeviceOperRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BizDeviceOperRecord> lqw = buildQueryWrapper(bo);
        Page<BizDeviceOperRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的设备操作记录列表
     *
     * @param bo 查询条件
     * @return 设备操作记录列表
     */
    @Override
    public List<BizDeviceOperRecordVo> queryList(BizDeviceOperRecordBo bo) {
        LambdaQueryWrapper<BizDeviceOperRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BizDeviceOperRecord> buildQueryWrapper(BizDeviceOperRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BizDeviceOperRecord> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(BizDeviceOperRecord::getId);
        lqw.eq(bo.getDeviceId() != null, BizDeviceOperRecord::getDeviceId, bo.getDeviceId());
        lqw.like(StringUtils.isNotBlank(bo.getDeviceName()), BizDeviceOperRecord::getDeviceName, bo.getDeviceName());
        lqw.eq(StringUtils.isNotBlank(bo.getOperType()), BizDeviceOperRecord::getOperType, bo.getOperType());
        lqw.eq(bo.getOperTime() != null, BizDeviceOperRecord::getOperTime, bo.getOperTime());
        lqw.eq(StringUtils.isNotBlank(bo.getPushContent()), BizDeviceOperRecord::getPushContent, bo.getPushContent());
        lqw.eq(StringUtils.isNotBlank(bo.getPushBatchNo()), BizDeviceOperRecord::getPushBatchNo, bo.getPushBatchNo());
        return lqw;
    }

    /**
     * 新增设备操作记录
     *
     * @param bo 设备操作记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BizDeviceOperRecordBo bo) {
        BizDeviceOperRecord add = MapstructUtils.convert(bo, BizDeviceOperRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改设备操作记录
     *
     * @param bo 设备操作记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BizDeviceOperRecordBo bo) {
        BizDeviceOperRecord update = MapstructUtils.convert(bo, BizDeviceOperRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BizDeviceOperRecord entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除设备操作记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
