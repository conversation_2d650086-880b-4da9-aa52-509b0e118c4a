package com.xu.biz.controller.web;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.xu.biz.domain.bo.BizDeviceBo;
import com.xu.biz.domain.vo.BizDeviceVo;
import com.xu.biz.service.IBizDeviceService;
import com.xu.common.core.domain.R;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.excel.utils.ExcelUtil;
import com.xu.common.idempotent.annotation.RepeatSubmit;
import com.xu.common.log.annotation.Log;
import com.xu.common.log.enums.BusinessType;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import com.xu.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 设备基础信息
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/biz/device")
public class BizDeviceController extends BaseController {

    private final IBizDeviceService bizDeviceService;

    /**
     * 查询设备基础信息列表
     */
    @SaCheckPermission("biz:device:list")
    @GetMapping("/list")
    public TableDataInfo<BizDeviceVo> list(BizDeviceBo bo, PageQuery pageQuery) {
        return bizDeviceService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出设备基础信息列表
     */
    @SaCheckPermission("biz:device:export")
    @Log(title = "设备基础信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BizDeviceBo bo, HttpServletResponse response) {
        List<BizDeviceVo> list = bizDeviceService.queryList(bo);
        ExcelUtil.exportExcel(list, "设备基础信息", BizDeviceVo.class, response);
    }

    /**
     * 获取设备基础信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:device:query")
    @GetMapping("/{id}")
    public R<BizDeviceVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(bizDeviceService.queryById(id));
    }

    /**
     * 新增设备基础信息
     */
    @SaCheckPermission("biz:device:add")
    @Log(title = "设备基础信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BizDeviceBo bo) {
        return toAjax(bizDeviceService.insertByBo(bo));
    }

    /**
     * 修改设备基础信息
     */
    @SaCheckPermission("biz:device:edit")
    @Log(title = "设备基础信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BizDeviceBo bo) {
        return toAjax(bizDeviceService.updateByBo(bo));
    }

    /**
     * 删除设备基础信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:device:remove")
    @Log(title = "设备基础信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(bizDeviceService.deleteWithValidByIds(List.of(ids), true));
    }


    /**
     * 播放策略配置
     */
    public R<Void> playStrategy(@RequestBody BizDeviceBo bo) {
        return toAjax(bizDeviceService.playStrategy(bo));
    }

    /**
     * 设备重启配置
     */
    public R<Void> restart(@RequestBody BizDeviceBo bo) {
        return toAjax(bizDeviceService.restart(bo));
    }

    /**
     * 强制出签
     */
    public R<Void> forcedShipments(@RequestBody BizDeviceBo bo) {
        return toAjax(bizDeviceService.forcedShipments(bo));
    }



}
