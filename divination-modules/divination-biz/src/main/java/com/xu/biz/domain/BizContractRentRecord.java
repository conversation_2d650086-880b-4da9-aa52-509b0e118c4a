package com.xu.biz.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 合同租金记录对象 biz_contract_rent_record
 *
 * <AUTHOR>
 * @date 2025-05-23
 * @deprecated 此类已废弃，请使用新的实现
 */
@Deprecated
@Data
@TableName("biz_contract_rent_record")
public class BizContractRentRecord{


    /**
     * 记录ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 合同ID
     */
    private Long contractId;

    /**
     * 缴纳人员
     */
    private String payPerson;

    /**
     * 缴纳时间
     */
    private Date payTime;

    /**
     * 缴纳金额
     */
    private Long payAmount;

    /**
     * 备注
     */
    private Date remark;

    /**
     * 删除标识 0否 1是
     */
    @TableLogic
    private Long delFlag;


}
