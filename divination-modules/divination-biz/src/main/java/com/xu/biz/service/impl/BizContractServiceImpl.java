package com.xu.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xu.biz.domain.BizContract;
import com.xu.biz.domain.bo.BizContractBo;
import com.xu.biz.domain.vo.BizContractVo;
import com.xu.biz.mapper.BizContractMapper;
import com.xu.biz.service.IBizContractService;
import com.xu.common.core.utils.MapstructUtils;
import com.xu.common.core.utils.StringUtils;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 合同管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RequiredArgsConstructor
@Service
public class BizContractServiceImpl implements IBizContractService {

    private final BizContractMapper baseMapper;

    /**
     * 查询合同管理
     *
     * @param id 主键
     * @return 合同管理
     */
    @Override
    public BizContractVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询合同管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 合同管理分页列表
     */
    @Override
    public TableDataInfo<BizContractVo> queryPageList(BizContractBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BizContract> lqw = buildQueryWrapper(bo);
        Page<BizContractVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的合同管理列表
     *
     * @param bo 查询条件
     * @return 合同管理列表
     */
    @Override
    public List<BizContractVo> queryList(BizContractBo bo) {
        LambdaQueryWrapper<BizContract> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BizContract> buildQueryWrapper(BizContractBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BizContract> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(BizContract::getId);
        lqw.like(StringUtils.isNotBlank(bo.getName()), BizContract::getName, bo.getName());
        lqw.eq(bo.getFileId() != null, BizContract::getFileId, bo.getFileId());
        lqw.eq(StringUtils.isNotBlank(bo.getFilePath()), BizContract::getFilePath, bo.getFilePath());
        lqw.eq(bo.getRentAmount() != null, BizContract::getRentAmount, bo.getRentAmount());
        lqw.like(StringUtils.isNotBlank(bo.getContactPerson()), BizContract::getContactPerson, bo.getContactPerson());
        lqw.like(StringUtils.isNotBlank(bo.getAddress()), BizContract::getAddress, bo.getAddress());
        lqw.ge(bo.getStartTime() != null, BizContract::getStartTime, bo.getStartTime());
        lqw.le(bo.getEndTime() != null, BizContract::getEndTime, bo.getEndTime());
        return lqw;
    }

    /**
     * 新增合同管理
     *
     * @param bo 合同管理
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BizContractBo bo) {
        BizContract add = MapstructUtils.convert(bo, BizContract.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改合同管理
     *
     * @param bo 合同管理
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BizContractBo bo) {
        BizContract update = MapstructUtils.convert(bo, BizContract.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BizContract entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除合同管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
