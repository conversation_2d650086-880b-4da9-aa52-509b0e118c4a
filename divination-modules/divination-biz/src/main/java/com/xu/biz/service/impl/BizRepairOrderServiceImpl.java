package com.xu.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xu.biz.domain.BizRepairOrder;
import com.xu.biz.domain.bo.BizRepairOrderBo;
import com.xu.biz.domain.vo.BizRepairOrderVo;
import com.xu.biz.mapper.BizRepairOrderMapper;
import com.xu.biz.service.IBizRepairOrderService;
import com.xu.common.core.utils.MapstructUtils;
import com.xu.common.core.utils.StringUtils;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 维修工单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RequiredArgsConstructor
@Service
public class BizRepairOrderServiceImpl implements IBizRepairOrderService {

    private final BizRepairOrderMapper baseMapper;

    /**
     * 查询维修工单
     *
     * @param id 主键
     * @return 维修工单
     */
    @Override
    public BizRepairOrderVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询维修工单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 维修工单分页列表
     */
    @Override
    public TableDataInfo<BizRepairOrderVo> queryPageList(BizRepairOrderBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BizRepairOrder> lqw = buildQueryWrapper(bo);
        Page<BizRepairOrderVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的维修工单列表
     *
     * @param bo 查询条件
     * @return 维修工单列表
     */
    @Override
    public List<BizRepairOrderVo> queryList(BizRepairOrderBo bo) {
        LambdaQueryWrapper<BizRepairOrder> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BizRepairOrder> buildQueryWrapper(BizRepairOrderBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BizRepairOrder> lqw = Wrappers.lambdaQuery();

        // 先按照处理状态排序：未处理>处理中>已完成>已取消
        lqw.orderByAsc(BizRepairOrder::getStatus);
        // 然后按照紧急程度由高到低排序
        lqw.orderByDesc(BizRepairOrder::getEmergencyLevel);
        // 最后按照时间顺序正序排序（假设是创建时间）
        lqw.orderByAsc(BizRepairOrder::getCreateTime);

        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), BizRepairOrder::getOrderNo, bo.getOrderNo());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceId()), BizRepairOrder::getDeviceId, bo.getDeviceId());
        lqw.like(StringUtils.isNotBlank(bo.getDeviceName()), BizRepairOrder::getDeviceName, bo.getDeviceName());
        lqw.eq(StringUtils.isNotBlank(bo.getFaultType()), BizRepairOrder::getFaultType, bo.getFaultType());
        lqw.eq(StringUtils.isNotBlank(bo.getFaultDesc()), BizRepairOrder::getFaultDesc, bo.getFaultDesc());
        lqw.like(StringUtils.isNotBlank(bo.getCustomerName()), BizRepairOrder::getCustomerName, bo.getCustomerName());
        lqw.eq(StringUtils.isNotBlank(bo.getCustomerPhone()), BizRepairOrder::getCustomerPhone, bo.getCustomerPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getCustomerAddress()), BizRepairOrder::getCustomerAddress, bo.getCustomerAddress());
        lqw.eq(bo.getEmergencyLevel() != null, BizRepairOrder::getEmergencyLevel, bo.getEmergencyLevel());
        lqw.eq(bo.getReportSource() != null, BizRepairOrder::getReportSource, bo.getReportSource());
        lqw.eq(bo.getStatus() != null, BizRepairOrder::getStatus, bo.getStatus());
        lqw.eq(bo.getRepairManId() != null, BizRepairOrder::getRepairManId, bo.getRepairManId());
        lqw.like(StringUtils.isNotBlank(bo.getRepairManName()), BizRepairOrder::getRepairManName, bo.getRepairManName());
        lqw.eq(StringUtils.isNotBlank(bo.getRepairManPhone()), BizRepairOrder::getRepairManPhone, bo.getRepairManPhone());
        lqw.eq(bo.getAppointmentTime() != null, BizRepairOrder::getAppointmentTime, bo.getAppointmentTime());
        lqw.eq(bo.getStartTime() != null, BizRepairOrder::getStartTime, bo.getStartTime());
        lqw.eq(bo.getFinishTime() != null, BizRepairOrder::getFinishTime, bo.getFinishTime());
        lqw.eq(StringUtils.isNotBlank(bo.getSolution()), BizRepairOrder::getSolution, bo.getSolution());
        lqw.eq(bo.getCustomerFeedback() != null, BizRepairOrder::getCustomerFeedback, bo.getCustomerFeedback());
        return lqw;
    }

    /**
     * 新增维修工单
     *
     * @param bo 维修工单
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BizRepairOrderBo bo) {
        BizRepairOrder add = MapstructUtils.convert(bo, BizRepairOrder.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改维修工单
     *
     * @param bo 维修工单
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BizRepairOrderBo bo) {
        BizRepairOrder update = MapstructUtils.convert(bo, BizRepairOrder.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BizRepairOrder entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除维修工单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
