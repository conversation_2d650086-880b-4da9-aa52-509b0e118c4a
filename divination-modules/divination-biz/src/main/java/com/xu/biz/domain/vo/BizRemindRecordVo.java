package com.xu.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xu.biz.domain.BizRemindRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 提醒记录视图对象 biz_remind_record
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BizRemindRecord.class)
public class BizRemindRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    private Long id;

    /**
     * 设备ID
     */
    @ExcelProperty(value = "设备ID")
    private Long deviceId;

    /**
     * 商品ID
     */
    @ExcelProperty(value = "商品ID")
    private Long goodsId;

    /**
     * 告警类型(1福不足 2签不足)
     */
    @ExcelProperty(value = "告警类型(1福不足 2签不足)")
    private Long alarmType;

    /**
     * 提醒类型(SMS短信/EMAIL邮箱)
     */
    @ExcelProperty(value = "提醒类型(SMS短信/EMAIL邮箱)")
    private String remindType;

    /**
     * 提醒时间
     */
    @ExcelProperty(value = "提醒时间")
    private Date remindTime;

    /**
     * 提醒人
     */
    @ExcelProperty(value = "提醒人")
    private String remindPerson;

    /**
     * 提醒内容
     */
    @ExcelProperty(value = "提醒内容")
    private String remindContent;


}
