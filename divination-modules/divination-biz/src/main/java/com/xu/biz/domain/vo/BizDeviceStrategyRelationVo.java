package com.xu.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xu.biz.domain.BizDeviceStrategyRelation;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;



/**
 * 设备播放策略关联视图对象 biz_device_strategy_relation
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BizDeviceStrategyRelation.class)
public class BizDeviceStrategyRelationVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 设备ID
     */
    @ExcelProperty(value = "设备ID")
    private Long deviceId;

    /**
     * 策略ID
     */
    @ExcelProperty(value = "策略ID")
    private Long strategyId;

    /**
     * 顺序
     */
    @ExcelProperty(value = "顺序")
    private Integer sort;


}
