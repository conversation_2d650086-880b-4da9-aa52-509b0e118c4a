package com.xu.biz.controller.web;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.xu.biz.domain.bo.BizAppUpgradePackageBo;
import com.xu.biz.domain.vo.BizAppUpgradePackageVo;
import com.xu.biz.service.IBizAppUpgradePackageService;
import com.xu.common.core.domain.R;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.excel.utils.ExcelUtil;
import com.xu.common.idempotent.annotation.RepeatSubmit;
import com.xu.common.log.annotation.Log;
import com.xu.common.log.enums.BusinessType;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import com.xu.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 安装包管理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/biz/appUpgradePackage")
public class BizAppUpgradePackageController extends BaseController {

    private final IBizAppUpgradePackageService bizAppUpgradePackageService;

    /**
     * 查询安装包管理列表
     */
    @SaCheckPermission("biz:appUpgradePackage:list")
    @GetMapping("/list")
    public TableDataInfo<BizAppUpgradePackageVo> list(BizAppUpgradePackageBo bo, PageQuery pageQuery) {
        return bizAppUpgradePackageService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出安装包管理列表
     */
    @SaCheckPermission("biz:appUpgradePackage:export")
    @Log(title = "安装包管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BizAppUpgradePackageBo bo, HttpServletResponse response) {
        List<BizAppUpgradePackageVo> list = bizAppUpgradePackageService.queryList(bo);
        ExcelUtil.exportExcel(list, "安装包管理", BizAppUpgradePackageVo.class, response);
    }

    /**
     * 获取安装包管理详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:appUpgradePackage:query")
    @GetMapping("/{id}")
    public R<BizAppUpgradePackageVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(bizAppUpgradePackageService.queryById(id));
    }

    /**
     * 新增安装包管理
     */
    @SaCheckPermission("biz:appUpgradePackage:add")
    @Log(title = "安装包管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BizAppUpgradePackageBo bo) {
        return toAjax(bizAppUpgradePackageService.insertByBo(bo));
    }

    /**
     * 修改安装包管理
     */
    @SaCheckPermission("biz:appUpgradePackage:edit")
    @Log(title = "安装包管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BizAppUpgradePackageBo bo) {
        return toAjax(bizAppUpgradePackageService.updateByBo(bo));
    }

    /**
     * 删除安装包管理
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:appUpgradePackage:remove")
    @Log(title = "安装包管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(bizAppUpgradePackageService.deleteWithValidByIds(List.of(ids), true));
    }
}
