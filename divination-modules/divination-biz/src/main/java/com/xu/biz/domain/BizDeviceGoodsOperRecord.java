package com.xu.biz.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xu.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 商品出补货记录对象 biz_device_goods_oper_record
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_device_goods_oper_record")
public class BizDeviceGoodsOperRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 配置ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 类型（OUT出货/ IN补货）
     */
    private String type;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 商品类型(FU 福/ QIAN 签)
     */
    private String goodsType;

    /**
     * 数量
     */
    private Long count;

    /**
     * 操作时间
     */
    private Date operTime;

    /**
     * 删除标识 0否 1是
     */
    @TableLogic
    private Long delFlag;


}
