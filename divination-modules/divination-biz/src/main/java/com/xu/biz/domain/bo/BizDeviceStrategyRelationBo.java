package com.xu.biz.domain.bo;

import com.xu.biz.domain.BizDeviceStrategyRelation;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备播放策略关联业务对象 biz_device_strategy_relation
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BizDeviceStrategyRelation.class, reverseConvertGenerate = false)
public class BizDeviceStrategyRelationBo extends BaseEntity {

    /**
     * 设备ID
     */
    @NotNull(message = "设备ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long deviceId;

    /**
     * 策略ID
     */
    @NotNull(message = "策略ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long strategyId;

    /**
     * 顺序
     */
    private Integer sort;


}
