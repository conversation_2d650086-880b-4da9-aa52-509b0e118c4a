package com.xu.biz.domain.bo;

import com.xu.biz.domain.BizAppUpgradeRecord;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * APP推送记录业务对象 biz_app_upgrade_record
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BizAppUpgradeRecord.class, reverseConvertGenerate = false)
public class BizAppUpgradeRecordBo extends BaseEntity {

    /**
     * 记录ID
     */
    @NotNull(message = "记录ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 关联任务ID
     */
    @NotNull(message = "关联任务ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long taskId;

    /**
     * 设备ID
     */
    @NotNull(message = "设备ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long deviceId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 安装包ID
     */
    private Long packageId;

    /**
     * 安装包名称
     */
    private String packageName;

    /**
     * 安装包数字编号
     */
    private Long packageCode;

    /**
     * 升级状态（0已推送 / 1成功 / 2失败）
     */
    @NotNull(message = "升级状态（0已推送 / 1成功 / 2失败）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer status;

    /**
     * 推送时间
     */
    private Date pushTime;


}
