package com.xu.biz.controller.web;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.xu.biz.domain.bo.BizRemindRecordBo;
import com.xu.biz.domain.vo.BizRemindRecordVo;
import com.xu.biz.service.IBizRemindRecordService;
import com.xu.common.core.domain.R;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.excel.utils.ExcelUtil;
import com.xu.common.idempotent.annotation.RepeatSubmit;
import com.xu.common.log.annotation.Log;
import com.xu.common.log.enums.BusinessType;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import com.xu.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 提醒记录
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/biz/remindRecord")
public class BizRemindRecordController extends BaseController {

    private final IBizRemindRecordService bizRemindRecordService;

    /**
     * 查询提醒记录列表
     */
    @SaCheckPermission("biz:remindRecord:list")
    @GetMapping("/list")
    public TableDataInfo<BizRemindRecordVo> list(BizRemindRecordBo bo, PageQuery pageQuery) {
        return bizRemindRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出提醒记录列表
     */
    @SaCheckPermission("biz:remindRecord:export")
    @Log(title = "提醒记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BizRemindRecordBo bo, HttpServletResponse response) {
        List<BizRemindRecordVo> list = bizRemindRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "提醒记录", BizRemindRecordVo.class, response);
    }

    /**
     * 获取提醒记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:remindRecord:query")
    @GetMapping("/{id}")
    public R<BizRemindRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(bizRemindRecordService.queryById(id));
    }

    /**
     * 新增提醒记录
     */
    @SaCheckPermission("biz:remindRecord:add")
    @Log(title = "提醒记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BizRemindRecordBo bo) {
        return toAjax(bizRemindRecordService.insertByBo(bo));
    }

    /**
     * 修改提醒记录
     */
    @SaCheckPermission("biz:remindRecord:edit")
    @Log(title = "提醒记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BizRemindRecordBo bo) {
        return toAjax(bizRemindRecordService.updateByBo(bo));
    }

    /**
     * 删除提醒记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:remindRecord:remove")
    @Log(title = "提醒记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(bizRemindRecordService.deleteWithValidByIds(List.of(ids), true));
    }
}
