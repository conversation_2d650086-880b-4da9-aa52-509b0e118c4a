package com.xu.biz.domain.bo;

import com.xu.biz.domain.BizSpace;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 空间管理业务对象 biz_space
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BizSpace.class, reverseConvertGenerate = false)
public class BizSpaceBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 空间名称
     */
    @NotBlank(message = "空间名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 空间编码
     */
    @NotBlank(message = "空间编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String code;

    /**
     * 描述
     */
    private String remark;

    /**
     * x坐标
     * @deprecated 此字段已废弃，请使用新的坐标系统
     */
    @Deprecated
    private String x;

    /**
     * y坐标
     * @deprecated 此字段已废弃，请使用新的坐标系统
     */
    @Deprecated
    private String y;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 父级ID
     */
    private Long parentId;


}
