package com.xu.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xu.biz.domain.BizPlayStrategy;
import com.xu.biz.domain.BizPlayStrategyMediaRelation;
import com.xu.biz.domain.bo.BizPlayStrategyBo;
import com.xu.biz.domain.vo.BizPlayStrategyVo;
import com.xu.biz.mapper.BizPlayStrategyMapper;
import com.xu.biz.mapper.BizPlayStrategyMediaRelationMapper;
import com.xu.biz.service.IBizPlayStrategyService;
import com.xu.common.core.utils.MapstructUtils;
import com.xu.common.core.utils.StringUtils;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 播放策略Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RequiredArgsConstructor
@Service
public class BizPlayStrategyServiceImpl implements IBizPlayStrategyService {

    private final BizPlayStrategyMapper baseMapper;
    private final BizPlayStrategyMediaRelationMapper relationMapper;

    /**
     * 查询播放策略
     *
     * @param id 主键
     * @return 播放策略
     */
    @Override
    public BizPlayStrategyVo queryById(Long id) {
        BizPlayStrategyVo bizPlayStrategyVo = baseMapper.selectVoById(id);
        List<BizPlayStrategyMediaRelation> mediaRelations = relationMapper.selectList(Wrappers.lambdaQuery(BizPlayStrategyMediaRelation.class)
            .eq(BizPlayStrategyMediaRelation::getStrategyId, id));
        Map<String, List<Long>> config = mediaRelations.stream()
            .collect(Collectors.groupingBy(
                BizPlayStrategyMediaRelation::getPlayType,
                Collectors.mapping(BizPlayStrategyMediaRelation::getMediaId, Collectors.toList())
            ));
        bizPlayStrategyVo.setMediaConfig(config);
        return bizPlayStrategyVo;
    }

    /**
     * 分页查询播放策略列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 播放策略分页列表
     */
    @Override
    public TableDataInfo<BizPlayStrategyVo> queryPageList(BizPlayStrategyBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BizPlayStrategy> lqw = buildQueryWrapper(bo);
        Page<BizPlayStrategyVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的播放策略列表
     *
     * @param bo 查询条件
     * @return 播放策略列表
     */
    @Override
    public List<BizPlayStrategyVo> queryList(BizPlayStrategyBo bo) {
        LambdaQueryWrapper<BizPlayStrategy> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BizPlayStrategy> buildQueryWrapper(BizPlayStrategyBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BizPlayStrategy> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(BizPlayStrategy::getId);
        lqw.like(StringUtils.isNotBlank(bo.getName()), BizPlayStrategy::getName, bo.getName());
        lqw.eq(bo.getPlayStrategy() != null, BizPlayStrategy::getPlayStrategy, bo.getPlayStrategy());
        lqw.eq(StringUtils.isNotBlank(bo.getDateType()), BizPlayStrategy::getDateType, bo.getDateType());
        lqw.eq(StringUtils.isNotBlank(bo.getDateContent()), BizPlayStrategy::getDateContent, bo.getDateContent());
        lqw.eq(bo.getStartTime() != null, BizPlayStrategy::getStartTime, bo.getStartTime());
        lqw.eq(bo.getEndTime() != null, BizPlayStrategy::getEndTime, bo.getEndTime());
        return lqw;
    }

    /**
     * 新增播放策略
     *
     * @param bo 播放策略
     * @return 是否新增成功
     */
    @Override
    @Transactional
    public Boolean insertByBo(BizPlayStrategyBo bo) {
        BizPlayStrategy add = MapstructUtils.convert(bo, BizPlayStrategy.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            Map<String, List<Long>> mediaConfig = bo.getMediaConfig();
            List<BizPlayStrategyMediaRelation> relations = mediaConfig.entrySet()
                .stream()
                .map(e -> {
                    String k = e.getKey();
                    List<Long> v = e.getValue();
                    List<BizPlayStrategyMediaRelation> list = new ArrayList<>();
                    for (int i = 0; i < v.size(); i++) {
                        BizPlayStrategyMediaRelation mediaRelation = new BizPlayStrategyMediaRelation();
                        mediaRelation.setStrategyId(add.getId());
                        mediaRelation.setMediaId(add.getId());
                        mediaRelation.setPlayType(k);
                        mediaRelation.setSort(i);
                        mediaRelation.setMediaId(v.get(i));
                        list.add(mediaRelation);
                    }
                    return list;
                })
                .flatMap(Collection::stream)
                .toList();
            relationMapper.insertBatch(relations);
        }
        return flag;
    }

    /**
     * 修改播放策略
     *
     * @param bo 播放策略
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BizPlayStrategyBo bo) {
        BizPlayStrategy update = MapstructUtils.convert(bo, BizPlayStrategy.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BizPlayStrategy entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除播放策略信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
