package com.xu.biz.service;


import com.xu.biz.domain.bo.BizDeviceGoodsOperRecordBo;
import com.xu.biz.domain.vo.BizDeviceGoodsOperRecordVo;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 商品出补货记录Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IBizDeviceGoodsOperRecordService {

    /**
     * 查询商品出补货记录
     *
     * @param id 主键
     * @return 商品出补货记录
     */
    BizDeviceGoodsOperRecordVo queryById(Long id);

    /**
     * 分页查询商品出补货记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 商品出补货记录分页列表
     */
    TableDataInfo<BizDeviceGoodsOperRecordVo> queryPageList(BizDeviceGoodsOperRecordBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的商品出补货记录列表
     *
     * @param bo 查询条件
     * @return 商品出补货记录列表
     */
    List<BizDeviceGoodsOperRecordVo> queryList(BizDeviceGoodsOperRecordBo bo);

    /**
     * 新增商品出补货记录
     *
     * @param bo 商品出补货记录
     * @return 是否新增成功
     */
    Boolean insertByBo(BizDeviceGoodsOperRecordBo bo);

    /**
     * 修改商品出补货记录
     *
     * @param bo 商品出补货记录
     * @return 是否修改成功
     */
    Boolean updateByBo(BizDeviceGoodsOperRecordBo bo);

    /**
     * 校验并批量删除商品出补货记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
