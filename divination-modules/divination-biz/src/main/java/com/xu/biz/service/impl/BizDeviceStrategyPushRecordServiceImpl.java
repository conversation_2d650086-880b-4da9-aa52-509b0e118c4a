package com.xu.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xu.biz.domain.BizDeviceStrategyPushRecord;
import com.xu.biz.domain.bo.BizDeviceStrategyPushRecordBo;
import com.xu.biz.domain.vo.BizDeviceStrategyPushRecordVo;
import com.xu.biz.mapper.BizDeviceStrategyPushRecordMapper;
import com.xu.biz.service.IBizDeviceStrategyPushRecordService;
import com.xu.common.core.utils.MapstructUtils;
import com.xu.common.core.utils.StringUtils;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 媒体推送记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RequiredArgsConstructor
@Service
public class BizDeviceStrategyPushRecordServiceImpl implements IBizDeviceStrategyPushRecordService {

    private final BizDeviceStrategyPushRecordMapper baseMapper;

    /**
     * 查询媒体推送记录
     *
     * @param id 主键
     * @return 媒体推送记录
     */
    @Override
    public BizDeviceStrategyPushRecordVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询媒体推送记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 媒体推送记录分页列表
     */
    @Override
    public TableDataInfo<BizDeviceStrategyPushRecordVo> queryPageList(BizDeviceStrategyPushRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BizDeviceStrategyPushRecord> lqw = buildQueryWrapper(bo);
        Page<BizDeviceStrategyPushRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的媒体推送记录列表
     *
     * @param bo 查询条件
     * @return 媒体推送记录列表
     */
    @Override
    public List<BizDeviceStrategyPushRecordVo> queryList(BizDeviceStrategyPushRecordBo bo) {
        LambdaQueryWrapper<BizDeviceStrategyPushRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BizDeviceStrategyPushRecord> buildQueryWrapper(BizDeviceStrategyPushRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BizDeviceStrategyPushRecord> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(BizDeviceStrategyPushRecord::getId);
        lqw.eq(bo.getStrategyId() != null, BizDeviceStrategyPushRecord::getStrategyId, bo.getStrategyId());
        lqw.like(StringUtils.isNotBlank(bo.getStrategyName()), BizDeviceStrategyPushRecord::getStrategyName, bo.getStrategyName());
        lqw.eq(bo.getDeviceId() != null, BizDeviceStrategyPushRecord::getDeviceId, bo.getDeviceId());
        lqw.like(StringUtils.isNotBlank(bo.getDeviceName()), BizDeviceStrategyPushRecord::getDeviceName, bo.getDeviceName());
        lqw.eq(StringUtils.isNotBlank(bo.getPushContent()), BizDeviceStrategyPushRecord::getPushContent, bo.getPushContent());
        lqw.eq(bo.getPushTime() != null, BizDeviceStrategyPushRecord::getPushTime, bo.getPushTime());
        lqw.eq(StringUtils.isNotBlank(bo.getPushBatchNo()), BizDeviceStrategyPushRecord::getPushBatchNo, bo.getPushBatchNo());
        return lqw;
    }

    /**
     * 新增媒体推送记录
     *
     * @param bo 媒体推送记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BizDeviceStrategyPushRecordBo bo) {
        BizDeviceStrategyPushRecord add = MapstructUtils.convert(bo, BizDeviceStrategyPushRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改媒体推送记录
     *
     * @param bo 媒体推送记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BizDeviceStrategyPushRecordBo bo) {
        BizDeviceStrategyPushRecord update = MapstructUtils.convert(bo, BizDeviceStrategyPushRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BizDeviceStrategyPushRecord entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除媒体推送记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
