package com.xu.biz.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xu.biz.domain.BizAppUpgradePackage;
import com.xu.biz.domain.bo.BizAppUpgradePackageBo;
import com.xu.biz.domain.vo.BizAppUpgradePackageVo;
import com.xu.biz.mapper.BizAppUpgradePackageMapper;
import com.xu.biz.service.IBizAppUpgradePackageService;
import com.xu.common.core.utils.MapstructUtils;
import com.xu.common.core.utils.StringUtils;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 安装包管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RequiredArgsConstructor
@Service
public class BizAppUpgradePackageServiceImpl implements IBizAppUpgradePackageService {

    private final BizAppUpgradePackageMapper baseMapper;

    /**
     * 查询安装包管理
     *
     * @param id 主键
     * @return 安装包管理
     */
    @Override
    public BizAppUpgradePackageVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询安装包管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 安装包管理分页列表
     */
    @Override
    public TableDataInfo<BizAppUpgradePackageVo> queryPageList(BizAppUpgradePackageBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BizAppUpgradePackage> lqw = buildQueryWrapper(bo);
        Page<BizAppUpgradePackageVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的安装包管理列表
     *
     * @param bo 查询条件
     * @return 安装包管理列表
     */
    @Override
    public List<BizAppUpgradePackageVo> queryList(BizAppUpgradePackageBo bo) {
        LambdaQueryWrapper<BizAppUpgradePackage> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BizAppUpgradePackage> buildQueryWrapper(BizAppUpgradePackageBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BizAppUpgradePackage> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(BizAppUpgradePackage::getId);
        lqw.like(StringUtils.isNotBlank(bo.getName()), BizAppUpgradePackage::getName, bo.getName());
        lqw.eq(bo.getCode() != null, BizAppUpgradePackage::getCode, bo.getCode());
        lqw.eq(StringUtils.isNotBlank(bo.getNumber()), BizAppUpgradePackage::getNumber, bo.getNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getFileId()), BizAppUpgradePackage::getFileId, bo.getFileId());
        lqw.eq(StringUtils.isNotBlank(bo.getFilePath()), BizAppUpgradePackage::getFilePath, bo.getFilePath());
        lqw.eq(StringUtils.isNotBlank(bo.getReleaseNotes()), BizAppUpgradePackage::getReleaseNotes, bo.getReleaseNotes());
        lqw.eq(bo.getForceUpdate() != null, BizAppUpgradePackage::getForceUpdate, bo.getForceUpdate());
        return lqw;
    }

    /**
     * 新增安装包管理
     *
     * @param bo 安装包管理
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BizAppUpgradePackageBo bo) {
        BizAppUpgradePackage add = MapstructUtils.convert(bo, BizAppUpgradePackage.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改安装包管理
     *
     * @param bo 安装包管理
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BizAppUpgradePackageBo bo) {
        BizAppUpgradePackage update = MapstructUtils.convert(bo, BizAppUpgradePackage.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BizAppUpgradePackage entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除安装包管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
