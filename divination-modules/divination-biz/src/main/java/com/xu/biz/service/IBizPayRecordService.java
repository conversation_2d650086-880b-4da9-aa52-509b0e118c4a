package com.xu.biz.service;


import com.xu.biz.domain.bo.BizPayRecordBo;
import com.xu.biz.domain.vo.BizPayRecordVo;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 支付交易记录Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IBizPayRecordService {

    /**
     * 查询支付交易记录
     *
     * @param id 主键
     * @return 支付交易记录
     */
    BizPayRecordVo queryById(Long id);

    /**
     * 分页查询支付交易记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 支付交易记录分页列表
     */
    TableDataInfo<BizPayRecordVo> queryPageList(BizPayRecordBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的支付交易记录列表
     *
     * @param bo 查询条件
     * @return 支付交易记录列表
     */
    List<BizPayRecordVo> queryList(BizPayRecordBo bo);

    /**
     * 新增支付交易记录
     *
     * @param bo 支付交易记录
     * @return 是否新增成功
     */
    Boolean insertByBo(BizPayRecordBo bo);

    /**
     * 修改支付交易记录
     *
     * @param bo 支付交易记录
     * @return 是否修改成功
     */
    Boolean updateByBo(BizPayRecordBo bo);

    /**
     * 校验并批量删除支付交易记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
