package com.xu.biz.controller.web;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.xu.biz.domain.bo.BizDeviceGoodsOperRecordBo;
import com.xu.biz.domain.vo.BizDeviceGoodsOperRecordVo;
import com.xu.biz.service.IBizDeviceGoodsOperRecordService;
import com.xu.common.core.domain.R;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.excel.utils.ExcelUtil;
import com.xu.common.idempotent.annotation.RepeatSubmit;
import com.xu.common.log.annotation.Log;
import com.xu.common.log.enums.BusinessType;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import com.xu.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
/**
 * 商品出补货记录
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/biz/deviceGoodsOperRecord")
public class BizDeviceGoodsOperRecordController extends BaseController {

    private final IBizDeviceGoodsOperRecordService bizDeviceGoodsOperRecordService;

    /**
     * 查询商品出补货记录列表
     */
    @SaCheckPermission("biz:deviceGoodsOperRecord:list")
    @GetMapping("/list")
    public TableDataInfo<BizDeviceGoodsOperRecordVo> list(BizDeviceGoodsOperRecordBo bo, PageQuery pageQuery) {
        return bizDeviceGoodsOperRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出商品出补货记录列表
     */
    @SaCheckPermission("biz:deviceGoodsOperRecord:export")
    @Log(title = "商品出补货记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BizDeviceGoodsOperRecordBo bo, HttpServletResponse response) {
        List<BizDeviceGoodsOperRecordVo> list = bizDeviceGoodsOperRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "商品出补货记录", BizDeviceGoodsOperRecordVo.class, response);
    }

    /**
     * 获取商品出补货记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:deviceGoodsOperRecord:query")
    @GetMapping("/{id}")
    public R<BizDeviceGoodsOperRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(bizDeviceGoodsOperRecordService.queryById(id));
    }

    /**
     * 新增商品出补货记录
     */
    @SaCheckPermission("biz:deviceGoodsOperRecord:add")
    @Log(title = "商品出补货记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BizDeviceGoodsOperRecordBo bo) {
        return toAjax(bizDeviceGoodsOperRecordService.insertByBo(bo));
    }

    /**
     * 修改商品出补货记录
     */
    @SaCheckPermission("biz:deviceGoodsOperRecord:edit")
    @Log(title = "商品出补货记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BizDeviceGoodsOperRecordBo bo) {
        return toAjax(bizDeviceGoodsOperRecordService.updateByBo(bo));
    }

    /**
     * 删除商品出补货记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:deviceGoodsOperRecord:remove")
    @Log(title = "商品出补货记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(bizDeviceGoodsOperRecordService.deleteWithValidByIds(List.of(ids), true));
    }
}
