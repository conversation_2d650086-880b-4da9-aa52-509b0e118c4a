package com.xu.biz.controller.web;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.xu.biz.domain.bo.BizPayRecordBo;
import com.xu.biz.domain.vo.BizPayRecordVo;
import com.xu.biz.service.IBizPayRecordService;
import com.xu.common.core.domain.R;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.excel.utils.ExcelUtil;
import com.xu.common.idempotent.annotation.RepeatSubmit;
import com.xu.common.log.annotation.Log;
import com.xu.common.log.enums.BusinessType;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import com.xu.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
/**
 * 支付交易记录
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/biz/payRecord")
public class BizPayRecordController extends BaseController {

    private final IBizPayRecordService bizPayRecordService;

    /**
     * 查询支付交易记录列表
     */
    @SaCheckPermission("biz:payRecord:list")
    @GetMapping("/list")
    public TableDataInfo<BizPayRecordVo> list(BizPayRecordBo bo, PageQuery pageQuery) {
        return bizPayRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出支付交易记录列表
     */
    @SaCheckPermission("biz:payRecord:export")
    @Log(title = "支付交易记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BizPayRecordBo bo, HttpServletResponse response) {
        List<BizPayRecordVo> list = bizPayRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "支付交易记录", BizPayRecordVo.class, response);
    }

    /**
     * 获取支付交易记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:payRecord:query")
    @GetMapping("/{id}")
    public R<BizPayRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(bizPayRecordService.queryById(id));
    }

    /**
     * 新增支付交易记录
     */
    @SaCheckPermission("biz:payRecord:add")
    @Log(title = "支付交易记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BizPayRecordBo bo) {
        return toAjax(bizPayRecordService.insertByBo(bo));
    }

    /**
     * 修改支付交易记录
     */
    @SaCheckPermission("biz:payRecord:edit")
    @Log(title = "支付交易记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BizPayRecordBo bo) {
        return toAjax(bizPayRecordService.updateByBo(bo));
    }

    /**
     * 删除支付交易记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:payRecord:remove")
    @Log(title = "支付交易记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(bizPayRecordService.deleteWithValidByIds(List.of(ids), true));
    }
}
