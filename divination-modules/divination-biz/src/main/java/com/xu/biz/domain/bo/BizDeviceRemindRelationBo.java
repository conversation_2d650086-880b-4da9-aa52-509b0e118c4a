package com.xu.biz.domain.bo;

import com.xu.biz.domain.BizDeviceRemindRelation;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备提醒关联业务对象 biz_device_remind_relation
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BizDeviceRemindRelation.class, reverseConvertGenerate = false)
public class BizDeviceRemindRelationBo extends BaseEntity {

    /**
     * 设备ID
     */
    @NotNull(message = "设备ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long deviceId;

    /**
     * 提醒方式（SMS短信/EMAIL邮件）
     */
    @NotNull(message = "提醒方式（SMS短信/EMAIL邮件）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long remindType;

    /**
     * 提醒内容
     */
    @NotNull(message = "提醒内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long remindContent;

    /**
     * 提醒人员
     */
    private String remindPerson;

    /**
     * 是否已经提醒
     */
    private Long reminded;


}
