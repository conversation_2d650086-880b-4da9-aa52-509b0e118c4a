package com.xu.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xu.biz.domain.BizDeviceGroupRelation;
import com.xu.biz.domain.bo.BizDeviceGroupRelationBo;
import com.xu.biz.domain.vo.BizDeviceGroupRelationVo;
import com.xu.biz.mapper.BizDeviceGroupRelationMapper;
import com.xu.biz.service.IBizDeviceGroupRelationService;
import com.xu.common.core.utils.MapstructUtils;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 设备分组关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RequiredArgsConstructor
@Service
@Deprecated
public class BizDeviceGroupRelationServiceImpl implements IBizDeviceGroupRelationService {

    private final BizDeviceGroupRelationMapper baseMapper;

    /**
     * 查询设备分组关联
     *
     * @param groupId 主键
     * @return 设备分组关联
     */
    @Override
    public BizDeviceGroupRelationVo queryById(Long groupId){
        return baseMapper.selectVoById(groupId);
    }

    /**
     * 分页查询设备分组关联列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 设备分组关联分页列表
     */
    @Override
    public TableDataInfo<BizDeviceGroupRelationVo> queryPageList(BizDeviceGroupRelationBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BizDeviceGroupRelation> lqw = buildQueryWrapper(bo);
        Page<BizDeviceGroupRelationVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的设备分组关联列表
     *
     * @param bo 查询条件
     * @return 设备分组关联列表
     */
    @Override
    public List<BizDeviceGroupRelationVo> queryList(BizDeviceGroupRelationBo bo) {
        LambdaQueryWrapper<BizDeviceGroupRelation> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BizDeviceGroupRelation> buildQueryWrapper(BizDeviceGroupRelationBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BizDeviceGroupRelation> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(BizDeviceGroupRelation::getGroupId);
        lqw.orderByAsc(BizDeviceGroupRelation::getDeviceId);
        return lqw;
    }

    /**
     * 新增设备分组关联
     *
     * @param bo 设备分组关联
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BizDeviceGroupRelationBo bo) {
        BizDeviceGroupRelation add = MapstructUtils.convert(bo, BizDeviceGroupRelation.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setGroupId(add.getGroupId());
        }
        return flag;
    }

    /**
     * 修改设备分组关联
     *
     * @param bo 设备分组关联
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BizDeviceGroupRelationBo bo) {
        BizDeviceGroupRelation update = MapstructUtils.convert(bo, BizDeviceGroupRelation.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BizDeviceGroupRelation entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除设备分组关联信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
