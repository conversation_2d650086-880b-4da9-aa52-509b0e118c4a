package com.xu.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xu.biz.domain.BizDeviceGoodsRelation;
import com.xu.biz.domain.bo.BizDeviceGoodsRelationBo;
import com.xu.biz.domain.vo.BizDeviceGoodsRelationVo;
import com.xu.biz.mapper.BizDeviceGoodsRelationMapper;
import com.xu.biz.service.IBizDeviceGoodsRelationService;
import com.xu.common.core.utils.MapstructUtils;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 设备商品关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RequiredArgsConstructor
@Service
public class BizDeviceGoodsRelationServiceImpl implements IBizDeviceGoodsRelationService {

    private final BizDeviceGoodsRelationMapper baseMapper;

    /**
     * 查询设备商品关联
     *
     * @param deviceId 主键
     * @return 设备商品关联
     */
    @Override
    public BizDeviceGoodsRelationVo queryById(Long deviceId){
        return baseMapper.selectVoById(deviceId);
    }

    /**
     * 分页查询设备商品关联列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 设备商品关联分页列表
     */
    @Override
    public TableDataInfo<BizDeviceGoodsRelationVo> queryPageList(BizDeviceGoodsRelationBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BizDeviceGoodsRelation> lqw = buildQueryWrapper(bo);
        Page<BizDeviceGoodsRelationVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的设备商品关联列表
     *
     * @param bo 查询条件
     * @return 设备商品关联列表
     */
    @Override
    public List<BizDeviceGoodsRelationVo> queryList(BizDeviceGoodsRelationBo bo) {
        LambdaQueryWrapper<BizDeviceGoodsRelation> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BizDeviceGoodsRelation> buildQueryWrapper(BizDeviceGoodsRelationBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BizDeviceGoodsRelation> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getDeviceId() != null, BizDeviceGoodsRelation::getDeviceId, bo.getDeviceId());
        lqw.eq(bo.getGoodsId() != null, BizDeviceGoodsRelation::getGoodsId, bo.getGoodsId());
        lqw.eq(bo.getCount() != null, BizDeviceGoodsRelation::getCount, bo.getCount());
        return lqw;
    }

    /**
     * 新增设备商品关联
     *
     * @param bo 设备商品关联
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BizDeviceGoodsRelationBo bo) {
        BizDeviceGoodsRelation add = MapstructUtils.convert(bo, BizDeviceGoodsRelation.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setDeviceId(add.getDeviceId());
        }
        return flag;
    }

    /**
     * 修改设备商品关联
     *
     * @param bo 设备商品关联
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BizDeviceGoodsRelationBo bo) {
        BizDeviceGoodsRelation update = MapstructUtils.convert(bo, BizDeviceGoodsRelation.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BizDeviceGoodsRelation entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除设备商品关联信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
