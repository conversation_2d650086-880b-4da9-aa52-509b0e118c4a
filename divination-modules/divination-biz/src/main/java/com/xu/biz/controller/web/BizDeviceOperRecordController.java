package com.xu.biz.controller.web;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.xu.biz.domain.bo.BizDeviceOperRecordBo;
import com.xu.biz.domain.vo.BizDeviceOperRecordVo;
import com.xu.biz.service.IBizDeviceOperRecordService;
import com.xu.common.core.domain.R;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.excel.utils.ExcelUtil;
import com.xu.common.idempotent.annotation.RepeatSubmit;
import com.xu.common.log.annotation.Log;
import com.xu.common.log.enums.BusinessType;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import com.xu.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
/**
 * 设备操作记录
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/biz/deviceOperRecord")
public class BizDeviceOperRecordController extends BaseController {

    private final IBizDeviceOperRecordService bizDeviceOperRecordService;

    /**
     * 查询设备操作记录列表
     */
    @SaCheckPermission("biz:deviceOperRecord:list")
    @GetMapping("/list")
    public TableDataInfo<BizDeviceOperRecordVo> list(BizDeviceOperRecordBo bo, PageQuery pageQuery) {
        return bizDeviceOperRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出设备操作记录列表
     */
    @SaCheckPermission("biz:deviceOperRecord:export")
    @Log(title = "设备操作记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BizDeviceOperRecordBo bo, HttpServletResponse response) {
        List<BizDeviceOperRecordVo> list = bizDeviceOperRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "设备操作记录", BizDeviceOperRecordVo.class, response);
    }

    /**
     * 获取设备操作记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:deviceOperRecord:query")
    @GetMapping("/{id}")
    public R<BizDeviceOperRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(bizDeviceOperRecordService.queryById(id));
    }

    /**
     * 新增设备操作记录
     */
    @SaCheckPermission("biz:deviceOperRecord:add")
    @Log(title = "设备操作记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BizDeviceOperRecordBo bo) {
        return toAjax(bizDeviceOperRecordService.insertByBo(bo));
    }

    /**
     * 修改设备操作记录
     */
    @SaCheckPermission("biz:deviceOperRecord:edit")
    @Log(title = "设备操作记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BizDeviceOperRecordBo bo) {
        return toAjax(bizDeviceOperRecordService.updateByBo(bo));
    }

    /**
     * 删除设备操作记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:deviceOperRecord:remove")
    @Log(title = "设备操作记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(bizDeviceOperRecordService.deleteWithValidByIds(List.of(ids), true));
    }
}
