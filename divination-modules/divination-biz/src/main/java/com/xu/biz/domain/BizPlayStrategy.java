package com.xu.biz.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xu.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 播放策略对象 biz_play_strategy
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_play_strategy")
public class BizPlayStrategy extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 策略名称
     */
    private String name;

    /**
     * 策略描述
     */
    private String remark;

    /**
     * 播放策略
     */
    private String playStrategy;

    /**
     * 日期类型（DAY日/WEEK周/MONTH月）
     */
    @Deprecated
    private String dateType;

    /**
     * 日期内容（日[EVERY_DAY]/周[1,2,3,4,5,6,7]/月[日期区间]）
     */
    @Deprecated
    private String dateContent;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 删除标识 0否 1是
     */
    @TableLogic
    private Long delFlag;


}
