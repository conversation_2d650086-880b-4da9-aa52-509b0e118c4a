package com.xu.biz.domain.bo;

import com.xu.biz.domain.BizGoods;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品业务对象 biz_goods
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BizGoods.class, reverseConvertGenerate = false)
public class BizGoodsBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 商品名称
     */
    @NotBlank(message = "商品名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 商品描述
     */
    private String remark;

    /**
     * 商品类型(字典)
     */
    private Long type;

    /**
     * 商品主图文件ID
     */
    private Long fileId;

    /**
     * 商品主图文件路径
     */
    private String filePath;

    /**
     * 商品单价
     */
    private Long price;


}
