package com.xu.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xu.biz.domain.BizRemindRecord;
import com.xu.biz.domain.bo.BizRemindRecordBo;
import com.xu.biz.domain.vo.BizRemindRecordVo;
import com.xu.biz.mapper.BizRemindRecordMapper;
import com.xu.biz.service.IBizRemindRecordService;
import com.xu.common.core.utils.MapstructUtils;
import com.xu.common.core.utils.StringUtils;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 提醒记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RequiredArgsConstructor
@Service
public class BizRemindRecordServiceImpl implements IBizRemindRecordService {

    private final BizRemindRecordMapper baseMapper;

    /**
     * 查询提醒记录
     *
     * @param id 主键
     * @return 提醒记录
     */
    @Override
    public BizRemindRecordVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询提醒记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 提醒记录分页列表
     */
    @Override
    public TableDataInfo<BizRemindRecordVo> queryPageList(BizRemindRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BizRemindRecord> lqw = buildQueryWrapper(bo);
        Page<BizRemindRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的提醒记录列表
     *
     * @param bo 查询条件
     * @return 提醒记录列表
     */
    @Override
    public List<BizRemindRecordVo> queryList(BizRemindRecordBo bo) {
        LambdaQueryWrapper<BizRemindRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BizRemindRecord> buildQueryWrapper(BizRemindRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BizRemindRecord> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(BizRemindRecord::getId);
        lqw.eq(bo.getDeviceId() != null, BizRemindRecord::getDeviceId, bo.getDeviceId());
        lqw.eq(bo.getGoodsId() != null, BizRemindRecord::getGoodsId, bo.getGoodsId());
        lqw.eq(bo.getAlarmType() != null, BizRemindRecord::getAlarmType, bo.getAlarmType());
        lqw.eq(StringUtils.isNotBlank(bo.getRemindType()), BizRemindRecord::getRemindType, bo.getRemindType());
        lqw.eq(bo.getRemindTime() != null, BizRemindRecord::getRemindTime, bo.getRemindTime());
        lqw.eq(StringUtils.isNotBlank(bo.getRemindPerson()), BizRemindRecord::getRemindPerson, bo.getRemindPerson());
        lqw.eq(StringUtils.isNotBlank(bo.getRemindContent()), BizRemindRecord::getRemindContent, bo.getRemindContent());
        return lqw;
    }

    /**
     * 新增提醒记录
     *
     * @param bo 提醒记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BizRemindRecordBo bo) {
        BizRemindRecord add = MapstructUtils.convert(bo, BizRemindRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改提醒记录
     *
     * @param bo 提醒记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BizRemindRecordBo bo) {
        BizRemindRecord update = MapstructUtils.convert(bo, BizRemindRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BizRemindRecord entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除提醒记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
