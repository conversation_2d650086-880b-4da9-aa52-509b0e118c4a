package com.xu.biz.controller.web;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.xu.biz.domain.bo.BizPlayMediaBo;
import com.xu.biz.domain.vo.BizPlayMediaVo;
import com.xu.biz.service.IBizPlayMediaService;
import com.xu.common.core.domain.R;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.excel.utils.ExcelUtil;
import com.xu.common.idempotent.annotation.RepeatSubmit;
import com.xu.common.log.annotation.Log;
import com.xu.common.log.enums.BusinessType;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import com.xu.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 播放媒体
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/biz/playMedia")
public class BizPlayMediaController extends BaseController {

    private final IBizPlayMediaService bizPlayMediaService;

    /**
     * 查询播放媒体列表
     */
    @SaCheckPermission("biz:playMedia:list")
    @GetMapping("/list")
    public TableDataInfo<BizPlayMediaVo> list(BizPlayMediaBo bo, PageQuery pageQuery) {
        return bizPlayMediaService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出播放媒体列表
     */
    @SaCheckPermission("biz:playMedia:export")
    @Log(title = "播放媒体", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BizPlayMediaBo bo, HttpServletResponse response) {
        List<BizPlayMediaVo> list = bizPlayMediaService.queryList(bo);
        ExcelUtil.exportExcel(list, "播放媒体", BizPlayMediaVo.class, response);
    }

    /**
     * 获取播放媒体详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:playMedia:query")
    @GetMapping("/{id}")
    public R<BizPlayMediaVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(bizPlayMediaService.queryById(id));
    }

    /**
     * 新增播放媒体
     */
    @SaCheckPermission("biz:playMedia:add")
    @Log(title = "播放媒体", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BizPlayMediaBo bo) {
        return toAjax(bizPlayMediaService.insertByBo(bo));
    }

    /**
     * 修改播放媒体
     */
    @SaCheckPermission("biz:playMedia:edit")
    @Log(title = "播放媒体", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BizPlayMediaBo bo) {
        return toAjax(bizPlayMediaService.updateByBo(bo));
    }

    /**
     * 删除播放媒体
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:playMedia:remove")
    @Log(title = "播放媒体", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(bizPlayMediaService.deleteWithValidByIds(List.of(ids), true));
    }
}
