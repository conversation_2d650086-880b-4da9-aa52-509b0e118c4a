package com.xu.biz.service;

import com.xu.biz.domain.bo.BizAppUpgradePackageBo;
import com.xu.biz.domain.vo.BizAppUpgradePackageVo;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 安装包管理Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IBizAppUpgradePackageService {

    /**
     * 查询安装包管理
     *
     * @param id 主键
     * @return 安装包管理
     */
    BizAppUpgradePackageVo queryById(Long id);

    /**
     * 分页查询安装包管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 安装包管理分页列表
     */
    TableDataInfo<BizAppUpgradePackageVo> queryPageList(BizAppUpgradePackageBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的安装包管理列表
     *
     * @param bo 查询条件
     * @return 安装包管理列表
     */
    List<BizAppUpgradePackageVo> queryList(BizAppUpgradePackageBo bo);

    /**
     * 新增安装包管理
     *
     * @param bo 安装包管理
     * @return 是否新增成功
     */
    Boolean insertByBo(BizAppUpgradePackageBo bo);

    /**
     * 修改安装包管理
     *
     * @param bo 安装包管理
     * @return 是否修改成功
     */
    Boolean updateByBo(BizAppUpgradePackageBo bo);

    /**
     * 校验并批量删除安装包管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
