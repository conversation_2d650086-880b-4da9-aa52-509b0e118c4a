package com.xu.biz.controller.web;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.xu.biz.domain.bo.BizPlayStrategyMediaRelationBo;
import com.xu.biz.domain.vo.BizPlayStrategyMediaRelationVo;
import com.xu.biz.service.IBizPlayStrategyMediaRelationService;
import com.xu.common.core.domain.R;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.excel.utils.ExcelUtil;
import com.xu.common.idempotent.annotation.RepeatSubmit;
import com.xu.common.log.annotation.Log;
import com.xu.common.log.enums.BusinessType;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import com.xu.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
/**
 * 播放策略媒体关联
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/biz/playStrategyMediaRelation")
public class BizPlayStrategyMediaRelationController extends BaseController {

    private final IBizPlayStrategyMediaRelationService bizPlayStrategyMediaRelationService;

    /**
     * 查询播放策略媒体关联列表
     */
    @SaCheckPermission("biz:playStrategyMediaRelation:list")
    @GetMapping("/list")
    public TableDataInfo<BizPlayStrategyMediaRelationVo> list(BizPlayStrategyMediaRelationBo bo, PageQuery pageQuery) {
        return bizPlayStrategyMediaRelationService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出播放策略媒体关联列表
     */
    @SaCheckPermission("biz:playStrategyMediaRelation:export")
    @Log(title = "播放策略媒体关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BizPlayStrategyMediaRelationBo bo, HttpServletResponse response) {
        List<BizPlayStrategyMediaRelationVo> list = bizPlayStrategyMediaRelationService.queryList(bo);
        ExcelUtil.exportExcel(list, "播放策略媒体关联", BizPlayStrategyMediaRelationVo.class, response);
    }

    /**
     * 获取播放策略媒体关联详细信息
     *
     * @param strategyId 主键
     */
    @SaCheckPermission("biz:playStrategyMediaRelation:query")
    @GetMapping("/{strategyId}")
    public R<BizPlayStrategyMediaRelationVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long strategyId) {
        return R.ok(bizPlayStrategyMediaRelationService.queryById(strategyId));
    }

    /**
     * 新增播放策略媒体关联
     */
    @SaCheckPermission("biz:playStrategyMediaRelation:add")
    @Log(title = "播放策略媒体关联", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BizPlayStrategyMediaRelationBo bo) {
        return toAjax(bizPlayStrategyMediaRelationService.insertByBo(bo));
    }

    /**
     * 修改播放策略媒体关联
     */
    @SaCheckPermission("biz:playStrategyMediaRelation:edit")
    @Log(title = "播放策略媒体关联", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BizPlayStrategyMediaRelationBo bo) {
        return toAjax(bizPlayStrategyMediaRelationService.updateByBo(bo));
    }

    /**
     * 删除播放策略媒体关联
     *
     * @param strategyIds 主键串
     */
    @SaCheckPermission("biz:playStrategyMediaRelation:remove")
    @Log(title = "播放策略媒体关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/{strategyIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] strategyIds) {
        return toAjax(bizPlayStrategyMediaRelationService.deleteWithValidByIds(List.of(strategyIds), true));
    }
}
