package com.xu.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xu.biz.domain.BizAppUpgradeRecord;
import com.xu.biz.domain.bo.BizAppUpgradeRecordBo;
import com.xu.biz.domain.vo.BizAppUpgradeRecordVo;
import com.xu.biz.mapper.BizAppUpgradeRecordMapper;
import com.xu.biz.service.IBizAppUpgradeRecordService;
import com.xu.common.core.utils.MapstructUtils;
import com.xu.common.core.utils.StringUtils;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * APP推送记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RequiredArgsConstructor
@Service
public class BizAppUpgradeRecordServiceImpl implements IBizAppUpgradeRecordService {

    private final BizAppUpgradeRecordMapper baseMapper;

    /**
     * 查询APP推送记录
     *
     * @param id 主键
     * @return APP推送记录
     */
    @Override
    public BizAppUpgradeRecordVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询APP推送记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return APP推送记录分页列表
     */
    @Override
    public TableDataInfo<BizAppUpgradeRecordVo> queryPageList(BizAppUpgradeRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BizAppUpgradeRecord> lqw = buildQueryWrapper(bo);
        Page<BizAppUpgradeRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的APP推送记录列表
     *
     * @param bo 查询条件
     * @return APP推送记录列表
     */
    @Override
    public List<BizAppUpgradeRecordVo> queryList(BizAppUpgradeRecordBo bo) {
        LambdaQueryWrapper<BizAppUpgradeRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BizAppUpgradeRecord> buildQueryWrapper(BizAppUpgradeRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BizAppUpgradeRecord> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(BizAppUpgradeRecord::getId);
        lqw.eq(bo.getTaskId() != null, BizAppUpgradeRecord::getTaskId, bo.getTaskId());
        lqw.eq(bo.getDeviceId() != null, BizAppUpgradeRecord::getDeviceId, bo.getDeviceId());
        lqw.like(StringUtils.isNotBlank(bo.getDeviceName()), BizAppUpgradeRecord::getDeviceName, bo.getDeviceName());
        lqw.eq(bo.getPackageId() != null, BizAppUpgradeRecord::getPackageId, bo.getPackageId());
        lqw.like(StringUtils.isNotBlank(bo.getPackageName()), BizAppUpgradeRecord::getPackageName, bo.getPackageName());
        lqw.eq(bo.getPackageCode() != null, BizAppUpgradeRecord::getPackageCode, bo.getPackageCode());
        lqw.eq(bo.getStatus() != null, BizAppUpgradeRecord::getStatus, bo.getStatus());
        lqw.eq(bo.getPushTime() != null, BizAppUpgradeRecord::getPushTime, bo.getPushTime());
        return lqw;
    }

    /**
     * 新增APP推送记录
     *
     * @param bo APP推送记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BizAppUpgradeRecordBo bo) {
        BizAppUpgradeRecord add = MapstructUtils.convert(bo, BizAppUpgradeRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改APP推送记录
     *
     * @param bo APP推送记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BizAppUpgradeRecordBo bo) {
        BizAppUpgradeRecord update = MapstructUtils.convert(bo, BizAppUpgradeRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BizAppUpgradeRecord entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除APP推送记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
