package com.xu.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xu.biz.domain.BizDeviceRemindRelation;
import com.xu.biz.domain.bo.BizDeviceRemindRelationBo;
import com.xu.biz.domain.vo.BizDeviceRemindRelationVo;
import com.xu.biz.mapper.BizDeviceRemindRelationMapper;
import com.xu.biz.service.IBizDeviceRemindRelationService;
import com.xu.common.core.utils.MapstructUtils;
import com.xu.common.core.utils.StringUtils;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 设备提醒关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RequiredArgsConstructor
@Service
public class BizDeviceRemindRelationServiceImpl implements IBizDeviceRemindRelationService {

    private final BizDeviceRemindRelationMapper baseMapper;

    /**
     * 查询设备提醒关联
     *
     * @param deviceId 主键
     * @return 设备提醒关联
     */
    @Override
    public BizDeviceRemindRelationVo queryById(Long deviceId){
        return baseMapper.selectVoById(deviceId);
    }

    /**
     * 分页查询设备提醒关联列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 设备提醒关联分页列表
     */
    @Override
    public TableDataInfo<BizDeviceRemindRelationVo> queryPageList(BizDeviceRemindRelationBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BizDeviceRemindRelation> lqw = buildQueryWrapper(bo);
        Page<BizDeviceRemindRelationVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的设备提醒关联列表
     *
     * @param bo 查询条件
     * @return 设备提醒关联列表
     */
    @Override
    public List<BizDeviceRemindRelationVo> queryList(BizDeviceRemindRelationBo bo) {
        LambdaQueryWrapper<BizDeviceRemindRelation> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BizDeviceRemindRelation> buildQueryWrapper(BizDeviceRemindRelationBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BizDeviceRemindRelation> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getDeviceId() != null, BizDeviceRemindRelation::getDeviceId, bo.getDeviceId());
        lqw.eq(bo.getRemindType() != null, BizDeviceRemindRelation::getRemindType, bo.getRemindType());
        lqw.eq(bo.getRemindContent() != null, BizDeviceRemindRelation::getRemindContent, bo.getRemindContent());
        lqw.eq(StringUtils.isNotBlank(bo.getRemindPerson()), BizDeviceRemindRelation::getRemindPerson, bo.getRemindPerson());
        lqw.eq(bo.getReminded() != null, BizDeviceRemindRelation::getReminded, bo.getReminded());
        return lqw;
    }

    /**
     * 新增设备提醒关联
     *
     * @param bo 设备提醒关联
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BizDeviceRemindRelationBo bo) {
        BizDeviceRemindRelation add = MapstructUtils.convert(bo, BizDeviceRemindRelation.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setDeviceId(add.getDeviceId());
        }
        return flag;
    }

    /**
     * 修改设备提醒关联
     *
     * @param bo 设备提醒关联
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BizDeviceRemindRelationBo bo) {
        BizDeviceRemindRelation update = MapstructUtils.convert(bo, BizDeviceRemindRelation.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BizDeviceRemindRelation entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除设备提醒关联信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
