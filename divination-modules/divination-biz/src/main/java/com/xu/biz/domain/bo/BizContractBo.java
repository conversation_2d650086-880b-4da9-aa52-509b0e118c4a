package com.xu.biz.domain.bo;

import com.xu.biz.domain.BizContract;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 合同管理业务对象 biz_contract
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BizContract.class, reverseConvertGenerate = false)
public class BizContractBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 描述
     */
    private String remark;

    /**
     * 文件ID
     */
    private Long fileId;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 租金(元/月)
     */
    private BigDecimal rentAmount;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 地址
     */
    private String address;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;


}
