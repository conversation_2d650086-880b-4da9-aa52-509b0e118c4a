package com.xu.biz.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xu.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 提醒记录对象 biz_remind_record
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_remind_record")
public class BizRemindRecord extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 告警类型(1福不足 2签不足)
     */
    private Long alarmType;

    /**
     * 提醒类型(SMS短信/EMAIL邮箱)
     */
    private String remindType;

    /**
     * 提醒时间
     */
    private Date remindTime;

    /**
     * 提醒人
     */
    private String remindPerson;

    /**
     * 提醒内容
     */
    private String remindContent;

    /**
     * 删除标识 0否 1是
     */
    @TableLogic
    private Long delFlag;


}
