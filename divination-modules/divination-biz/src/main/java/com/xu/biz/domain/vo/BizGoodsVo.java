package com.xu.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xu.biz.domain.BizGoods;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;



/**
 * 商品视图对象 biz_goods
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BizGoods.class)
public class BizGoodsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称")
    private String name;

    /**
     * 商品描述
     */
    @ExcelProperty(value = "商品描述")
    private String remark;

    /**
     * 商品类型(字典)
     */
    @ExcelProperty(value = "商品类型")
    private Long type;

    /**
     * 商品主图文件ID
     */
    @ExcelProperty(value = "商品主图文件ID")
    private Long fileId;

    /**
     * 商品主图文件路径
     */
    @ExcelProperty(value = "商品主图文件路径")
    private String filePath;

    /**
     * 商品单价
     */
    @ExcelProperty(value = "商品单价")
    private Long price;


}
