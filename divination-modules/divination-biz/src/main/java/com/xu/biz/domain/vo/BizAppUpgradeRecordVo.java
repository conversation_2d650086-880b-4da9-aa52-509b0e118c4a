package com.xu.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xu.biz.domain.BizAppUpgradeRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * APP推送记录视图对象 biz_app_upgrade_record
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BizAppUpgradeRecord.class)
public class BizAppUpgradeRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    private Long id;

    /**
     * 关联任务ID
     */
    @ExcelProperty(value = "关联任务ID")
    private Long taskId;

    /**
     * 设备ID
     */
    @ExcelProperty(value = "设备ID")
    private Long deviceId;

    /**
     * 设备名称
     */
    @ExcelProperty(value = "设备名称")
    private String deviceName;

    /**
     * 安装包ID
     */
    @ExcelProperty(value = "安装包ID")
    private Long packageId;

    /**
     * 安装包名称
     */
    @ExcelProperty(value = "安装包名称")
    private String packageName;

    /**
     * 安装包数字编号
     */
    @ExcelProperty(value = "安装包数字编号")
    private Long packageCode;

    /**
     * 升级状态（0已推送 / 1成功 / 2失败）
     */
    @ExcelProperty(value = "升级状态")
    private Integer status;

    /**
     * 推送时间
     */
    @ExcelProperty(value = "推送时间")
    private Date pushTime;


}
