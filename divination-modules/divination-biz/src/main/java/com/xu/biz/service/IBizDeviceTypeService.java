package com.xu.biz.service;


import com.xu.biz.domain.bo.BizDeviceTypeBo;
import com.xu.biz.domain.vo.BizDeviceTypeVo;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 设备类型Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IBizDeviceTypeService {

    /**
     * 查询设备类型
     *
     * @param id 主键
     * @return 设备类型
     */
    BizDeviceTypeVo queryById(Long id);

    /**
     * 分页查询设备类型列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 设备类型分页列表
     */
    TableDataInfo<BizDeviceTypeVo> queryPageList(BizDeviceTypeBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的设备类型列表
     *
     * @param bo 查询条件
     * @return 设备类型列表
     */
    List<BizDeviceTypeVo> queryList(BizDeviceTypeBo bo);

    /**
     * 新增设备类型
     *
     * @param bo 设备类型
     * @return 是否新增成功
     */
    Boolean insertByBo(BizDeviceTypeBo bo);

    /**
     * 修改设备类型
     *
     * @param bo 设备类型
     * @return 是否修改成功
     */
    Boolean updateByBo(BizDeviceTypeBo bo);

    /**
     * 校验并批量删除设备类型信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
