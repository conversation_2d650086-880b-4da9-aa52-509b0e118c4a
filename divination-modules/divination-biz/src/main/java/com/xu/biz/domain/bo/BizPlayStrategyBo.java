package com.xu.biz.domain.bo;

import com.xu.biz.domain.BizPlayStrategy;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 播放策略业务对象 biz_play_strategy
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BizPlayStrategy.class, reverseConvertGenerate = false)
public class BizPlayStrategyBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 策略名称
     */
    @NotBlank(message = "策略名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String name;

    /**
     * 策略描述
     */
    private String remark;

    /**
     * 播放策略(字典)
     */
    @NotNull(message = "播放策略不能为空", groups = {AddGroup.class, EditGroup.class})
    private String playStrategy;

    /**
     * 日期类型（DAY日/WEEK周/MONTH月）
     *
     * @deprecated 该字段已废弃，请不要使用
     */
    @Deprecated
    private String dateType;

    /**
     * 日期内容（日[EVERY_DAY]/周[1,2,3,4,5,6,7]/月[日期区间]）
     *
     * @deprecated 该字段已废弃，请不要使用
     */
    @Deprecated
    private String dateContent;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 播放内容
     */
    private Map<String, List<Long>> mediaConfig;


}
