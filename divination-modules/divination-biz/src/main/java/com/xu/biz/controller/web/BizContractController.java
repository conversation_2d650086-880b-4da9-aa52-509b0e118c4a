package com.xu.biz.controller.web;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.xu.biz.domain.bo.BizContractBo;
import com.xu.biz.domain.vo.BizContractVo;
import com.xu.biz.service.IBizContractService;
import com.xu.common.core.domain.R;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.excel.utils.ExcelUtil;
import com.xu.common.idempotent.annotation.RepeatSubmit;
import com.xu.common.log.annotation.Log;
import com.xu.common.log.enums.BusinessType;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import com.xu.common.web.core.BaseController;
import com.xu.system.service.ISysFileService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 合同管理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/biz/contract")
public class BizContractController extends BaseController {

    private final IBizContractService bizContractService;
    private final ISysFileService sysFileService;

    /**
     * 查询合同管理列表
     */
    @SaCheckPermission("biz:contract:list")
    @GetMapping("/list")
    public TableDataInfo<BizContractVo> list(BizContractBo bo, PageQuery pageQuery) {
        return bizContractService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出合同管理列表
     */
    @SaCheckPermission("biz:contract:export")
    @Log(title = "合同管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BizContractBo bo, HttpServletResponse response) {
        List<BizContractVo> list = bizContractService.queryList(bo);
        ExcelUtil.exportExcel(list, "合同管理", BizContractVo.class, response);
    }

    /**
     * 获取合同管理详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:contract:query")
    @GetMapping("/{id}")
    public R<BizContractVo> getInfo(@NotNull(message = "主键不能为空")
                                    @PathVariable Long id) {
        return R.ok(bizContractService.queryById(id));
    }

    /**
     * 新增合同管理
     */
    @SaCheckPermission("biz:contract:add")
    @Log(title = "合同管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BizContractBo bo) {
        return toAjax(bizContractService.insertByBo(bo));
    }

    /**
     * 修改合同管理
     */
    @SaCheckPermission("biz:contract:edit")
    @Log(title = "合同管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BizContractBo bo) {
        return toAjax(bizContractService.updateByBo(bo));
    }

    /**
     * 删除合同管理
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:contract:remove")
    @Log(title = "合同管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(bizContractService.deleteWithValidByIds(List.of(ids), true));
    }


}
