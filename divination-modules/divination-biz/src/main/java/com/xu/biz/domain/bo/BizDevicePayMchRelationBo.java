package com.xu.biz.domain.bo;

import com.xu.biz.domain.BizDevicePayMchRelation;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备支付商户关联业务对象 biz_device_pay_mch_relation
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BizDevicePayMchRelation.class, reverseConvertGenerate = false)
public class BizDevicePayMchRelationBo extends BaseEntity {

    /**
     * 设备ID
     */
    @NotNull(message = "设备ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long deviceId;

    /**
     * 支付服务商配置ID
     */
    @NotNull(message = "支付服务商配置ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long payMchId;


}
