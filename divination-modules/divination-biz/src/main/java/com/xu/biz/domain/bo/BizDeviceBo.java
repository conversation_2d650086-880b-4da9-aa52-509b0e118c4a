package com.xu.biz.domain.bo;

import com.xu.biz.domain.BizDevice;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 设备基础信息业务对象 biz_device
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BizDevice.class, reverseConvertGenerate = false)
public class BizDeviceBo extends BaseEntity {

    /**
     * 设备ID
     */
    @NotNull(message = "设备ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 设备编码
     */
    private String code;

    /**
     * 设备名称
     */
    @NotBlank(message = "设备名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 位置描述
     */
    private String location;

    /**
     * 区域编码
     */
    private String areaCode;

    /**
     * 类型ID
     */
    private Long typeId;

    /**
     * 分组ID
     */
    private Long groupId;

    /**
     * 当地联系人
     */
    private String contact;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 开启状态(0-禁用 1-启用)
     */
    @NotNull(message = "开启状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String opneStatus;

    /**
     * 在线状态(0-离线 1-在线)
     */
    private String onlineStatus;

    /**
     * 维修状态(0-正常 1-损坏 2维修中)
     */
    private String repairStatus;

    /**
     * 设备租金(元/月)
     */
    private Long rent;

    /**
     * 最后心跳时间
     */
    private Date lastHeartbeat;

    /**
     * 设备IP
     */
    private String ip;

    /**
     * MAC地址
     */
    private String mac;

    /**
     * 应用版本号
     */
    private String appNumber;

    /**
     * 常量时间/s
     */
    private Long everBrightTime;
}
