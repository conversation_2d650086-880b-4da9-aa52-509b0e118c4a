package com.xu.biz.controller.web;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.xu.biz.domain.bo.BizDevicePayMchRelationBo;
import com.xu.biz.domain.vo.BizDevicePayMchRelationVo;
import com.xu.biz.service.IBizDevicePayMchRelationService;
import com.xu.common.core.domain.R;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.excel.utils.ExcelUtil;
import com.xu.common.idempotent.annotation.RepeatSubmit;
import com.xu.common.log.annotation.Log;
import com.xu.common.log.enums.BusinessType;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import com.xu.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
/**
 * 设备支付商户关联
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/biz/devicePayMchRelation")
public class BizDevicePayMchRelationController extends BaseController {

    private final IBizDevicePayMchRelationService bizDevicePayMchRelationService;

    /**
     * 查询设备支付商户关联列表
     */
    @SaCheckPermission("biz:devicePayMchRelation:list")
    @GetMapping("/list")
    public TableDataInfo<BizDevicePayMchRelationVo> list(BizDevicePayMchRelationBo bo, PageQuery pageQuery) {
        return bizDevicePayMchRelationService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出设备支付商户关联列表
     */
    @SaCheckPermission("biz:devicePayMchRelation:export")
    @Log(title = "设备支付商户关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BizDevicePayMchRelationBo bo, HttpServletResponse response) {
        List<BizDevicePayMchRelationVo> list = bizDevicePayMchRelationService.queryList(bo);
        ExcelUtil.exportExcel(list, "设备支付商户关联", BizDevicePayMchRelationVo.class, response);
    }

    /**
     * 获取设备支付商户关联详细信息
     *
     * @param deviceId 主键
     */
    @SaCheckPermission("biz:devicePayMchRelation:query")
    @GetMapping("/{deviceId}")
    public R<BizDevicePayMchRelationVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long deviceId) {
        return R.ok(bizDevicePayMchRelationService.queryById(deviceId));
    }

    /**
     * 新增设备支付商户关联
     */
    @SaCheckPermission("biz:devicePayMchRelation:add")
    @Log(title = "设备支付商户关联", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BizDevicePayMchRelationBo bo) {
        return toAjax(bizDevicePayMchRelationService.insertByBo(bo));
    }

    /**
     * 修改设备支付商户关联
     */
    @SaCheckPermission("biz:devicePayMchRelation:edit")
    @Log(title = "设备支付商户关联", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BizDevicePayMchRelationBo bo) {
        return toAjax(bizDevicePayMchRelationService.updateByBo(bo));
    }

    /**
     * 删除设备支付商户关联
     *
     * @param deviceIds 主键串
     */
    @SaCheckPermission("biz:devicePayMchRelation:remove")
    @Log(title = "设备支付商户关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deviceIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] deviceIds) {
        return toAjax(bizDevicePayMchRelationService.deleteWithValidByIds(List.of(deviceIds), true));
    }
}
