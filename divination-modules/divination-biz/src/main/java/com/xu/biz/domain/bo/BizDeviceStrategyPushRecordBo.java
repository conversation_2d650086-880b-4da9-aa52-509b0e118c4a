package com.xu.biz.domain.bo;

import com.xu.biz.domain.BizDeviceStrategyPushRecord;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 媒体推送记录业务对象 biz_device_strategy_push_record
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BizDeviceStrategyPushRecord.class, reverseConvertGenerate = false)
public class BizDeviceStrategyPushRecordBo extends BaseEntity {

    /**
     * 记录ID
     */
    @NotNull(message = "记录ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 策略ID
     */
    @NotNull(message = "策略ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long strategyId;

    /**
     * 策略名称
     */
    private String strategyName;

    /**
     * 设备ID
     */
    @NotNull(message = "设备ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long deviceId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 推送内容
     */
    private String pushContent;

    /**
     * 推送时间
     */
    @NotNull(message = "推送时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date pushTime;

    /**
     * 推送批次号
     */
    @NotBlank(message = "推送批次号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String pushBatchNo;


}
