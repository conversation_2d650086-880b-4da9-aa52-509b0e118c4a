package com.xu.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xu.biz.domain.BizDeviceGoodsOperRecord;
import com.xu.biz.domain.bo.BizDeviceGoodsOperRecordBo;
import com.xu.biz.domain.vo.BizDeviceGoodsOperRecordVo;
import com.xu.biz.mapper.BizDeviceGoodsOperRecordMapper;
import com.xu.biz.service.IBizDeviceGoodsOperRecordService;
import com.xu.common.core.utils.MapstructUtils;
import com.xu.common.core.utils.StringUtils;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 商品出补货记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RequiredArgsConstructor
@Service
public class BizDeviceGoodsOperRecordServiceImpl implements IBizDeviceGoodsOperRecordService {

    private final BizDeviceGoodsOperRecordMapper baseMapper;

    /**
     * 查询商品出补货记录
     *
     * @param id 主键
     * @return 商品出补货记录
     */
    @Override
    public BizDeviceGoodsOperRecordVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询商品出补货记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 商品出补货记录分页列表
     */
    @Override
    public TableDataInfo<BizDeviceGoodsOperRecordVo> queryPageList(BizDeviceGoodsOperRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BizDeviceGoodsOperRecord> lqw = buildQueryWrapper(bo);
        Page<BizDeviceGoodsOperRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的商品出补货记录列表
     *
     * @param bo 查询条件
     * @return 商品出补货记录列表
     */
    @Override
    public List<BizDeviceGoodsOperRecordVo> queryList(BizDeviceGoodsOperRecordBo bo) {
        LambdaQueryWrapper<BizDeviceGoodsOperRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BizDeviceGoodsOperRecord> buildQueryWrapper(BizDeviceGoodsOperRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BizDeviceGoodsOperRecord> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(BizDeviceGoodsOperRecord::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getType()), BizDeviceGoodsOperRecord::getType, bo.getType());
        lqw.eq(bo.getDeviceId() != null, BizDeviceGoodsOperRecord::getDeviceId, bo.getDeviceId());
        lqw.like(StringUtils.isNotBlank(bo.getDeviceName()), BizDeviceGoodsOperRecord::getDeviceName, bo.getDeviceName());
        lqw.eq(bo.getGoodsId() != null, BizDeviceGoodsOperRecord::getGoodsId, bo.getGoodsId());
        lqw.eq(StringUtils.isNotBlank(bo.getGoodsType()), BizDeviceGoodsOperRecord::getGoodsType, bo.getGoodsType());
        lqw.eq(bo.getCount() != null, BizDeviceGoodsOperRecord::getCount, bo.getCount());
        lqw.eq(bo.getOperTime() != null, BizDeviceGoodsOperRecord::getOperTime, bo.getOperTime());
        return lqw;
    }

    /**
     * 新增商品出补货记录
     *
     * @param bo 商品出补货记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BizDeviceGoodsOperRecordBo bo) {
        BizDeviceGoodsOperRecord add = MapstructUtils.convert(bo, BizDeviceGoodsOperRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改商品出补货记录
     *
     * @param bo 商品出补货记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BizDeviceGoodsOperRecordBo bo) {
        BizDeviceGoodsOperRecord update = MapstructUtils.convert(bo, BizDeviceGoodsOperRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BizDeviceGoodsOperRecord entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除商品出补货记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
