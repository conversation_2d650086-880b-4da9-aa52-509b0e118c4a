package com.xu.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xu.biz.domain.BizRepairOrder;
import com.xu.common.excel.annotation.ExcelDictFormat;
import com.xu.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 维修工单视图对象 biz_repair_order
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BizRepairOrder.class)
public class BizRepairOrderVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 维修单ID
     */
    private Long id;

    /**
     * 维修单编号
     */
    @ExcelProperty(value = "维修单编号")
    private String orderNo;

    /**
     * 终端设备ID
     */
    private String deviceId;

    /**
     * 终端名称
     */
    @ExcelProperty(value = "终端名称")
    private String deviceName;

    /**
     * 故障类型[NO_SIGN:不出签,NO_SYMBOL:不出符,NO_QRCODE:不显示二维码,PAY_FAIL:支付失败,OTHER:其他]
     */
    @ExcelDictFormat(dictType = "device_bug_type")
    @ExcelProperty(value = "故障类型",converter = ExcelDictConvert.class)
    private String faultType;

    /**
     * 故障详细描述
     */
    @ExcelProperty(value = "故障详细描述")
    private String faultDesc;

    /**
     * 客户姓名
     */
    @ExcelProperty(value = "客户姓名")
    private String customerName;

    /**
     * 客户电话
     */
    @ExcelProperty(value = "客户电话")
    private String customerPhone;

    /**
     * 客户地址
     */
    @ExcelProperty(value = "客户地址")
    private String customerAddress;

    /**
     * 紧急程度(字典)
     */
    @ExcelDictFormat(dictType = "degree_of_urgency")
    @ExcelProperty(value = "紧急程度", converter = ExcelDictConvert.class)
    private String emergencyLevel;

    /**
     * 报修来源(1-电话报修 2-终端上报 3-平台录入)
     */
    @ExcelDictFormat(readConverterExp = "1=电话报修,2=终端上报,3=平台录入")
    @ExcelProperty(value = "报修来源", converter = ExcelDictConvert.class)
    private Integer reportSource;

    /**
     * 维修状态(0-待处理 1-处理中 3-已完成 4-已取消)
     */
    @ExcelDictFormat(readConverterExp = "0=待处理,1=处理中,3=已完成,4=已取消")
    @ExcelProperty(value = "维修状态",converter = ExcelDictConvert.class)
    private Integer status;

    /**
     * 维修人员ID
     */
    private Long repairManId;

    /**
     * 维修人员姓名
     */
    @ExcelProperty(value = "维修人员姓名")
    private String repairManName;

    /**
     * 维修人员电话
     */
    @ExcelProperty(value = "维修人员电话")
    private String repairManPhone;

    /**
     * 预约上门时间
     */
    @ExcelProperty(value = "预约上门时间")
    private Date appointmentTime;

    /**
     * 维修开始时间
     */
    @ExcelProperty(value = "维修开始时间")
    private Date startTime;

    /**
     * 维修完成时间
     */
    @ExcelProperty(value = "维修完成时间")
    private Date finishTime;

    /**
     * 处理结果
     */
    @ExcelProperty(value = "处理结果")
    private String solution;

    /**
     * 客户反馈(1-满意 2-一般 3-不满意)
     */
    @ExcelDictFormat(dictType = "customer_feed_back")
    @ExcelProperty(value = "客户反馈",converter = ExcelDictConvert.class)
    private Long customerFeedback;


}
