package com.xu.biz.service;


import com.xu.biz.domain.bo.BizContractBo;
import com.xu.biz.domain.vo.BizContractVo;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 合同管理Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IBizContractService {

    /**
     * 查询合同管理
     *
     * @param id 主键
     * @return 合同管理
     */
    BizContractVo queryById(Long id);

    /**
     * 分页查询合同管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 合同管理分页列表
     */
    TableDataInfo<BizContractVo> queryPageList(BizContractBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的合同管理列表
     *
     * @param bo 查询条件
     * @return 合同管理列表
     */
    List<BizContractVo> queryList(BizContractBo bo);

    /**
     * 新增合同管理
     *
     * @param bo 合同管理
     * @return 是否新增成功
     */
    Boolean insertByBo(BizContractBo bo);

    /**
     * 修改合同管理
     *
     * @param bo 合同管理
     * @return 是否修改成功
     */
    Boolean updateByBo(BizContractBo bo);

    /**
     * 校验并批量删除合同管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
