package com.xu.biz.controller.web;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.xu.biz.domain.bo.BizAppUpgradeTaskBo;
import com.xu.biz.domain.vo.BizAppUpgradeTaskVo;
import com.xu.biz.service.IBizAppUpgradeTaskService;
import com.xu.common.core.domain.R;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.excel.utils.ExcelUtil;
import com.xu.common.idempotent.annotation.RepeatSubmit;
import com.xu.common.log.annotation.Log;
import com.xu.common.log.enums.BusinessType;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import com.xu.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 升级计划
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/biz/appUpgradeTask")
public class BizAppUpgradeTaskController extends BaseController {

    private final IBizAppUpgradeTaskService bizAppUpgradeTaskService;

    /**
     * 查询升级计划列表
     */
    @SaCheckPermission("biz:appUpgradeTask:list")
    @GetMapping("/list")
    public TableDataInfo<BizAppUpgradeTaskVo> list(BizAppUpgradeTaskBo bo, PageQuery pageQuery) {
        return bizAppUpgradeTaskService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出升级计划列表
     */
    @SaCheckPermission("biz:appUpgradeTask:export")
    @Log(title = "升级计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BizAppUpgradeTaskBo bo, HttpServletResponse response) {
        List<BizAppUpgradeTaskVo> list = bizAppUpgradeTaskService.queryList(bo);
        ExcelUtil.exportExcel(list, "升级计划", BizAppUpgradeTaskVo.class, response);
    }

    /**
     * 获取升级计划详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:appUpgradeTask:query")
    @GetMapping("/{id}")
    public R<BizAppUpgradeTaskVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(bizAppUpgradeTaskService.queryById(id));
    }

    /**
     * 新增升级计划
     */
    @SaCheckPermission("biz:appUpgradeTask:add")
    @Log(title = "升级计划", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BizAppUpgradeTaskBo bo) {
        return toAjax(bizAppUpgradeTaskService.insertByBo(bo));
    }

    /**
     * 修改升级计划
     */
    @SaCheckPermission("biz:appUpgradeTask:edit")
    @Log(title = "升级计划", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BizAppUpgradeTaskBo bo) {
        return toAjax(bizAppUpgradeTaskService.updateByBo(bo));
    }

    /**
     * 删除升级计划
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:appUpgradeTask:remove")
    @Log(title = "升级计划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(bizAppUpgradeTaskService.deleteWithValidByIds(List.of(ids), true));
    }
}
