package com.xu.biz.service;


import com.xu.biz.domain.bo.BizGoodsBo;
import com.xu.biz.domain.vo.BizGoodsVo;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 商品Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IBizGoodsService {

    /**
     * 查询商品
     *
     * @param id 主键
     * @return 商品
     */
    BizGoodsVo queryById(Long id);

    /**
     * 分页查询商品列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 商品分页列表
     */
    TableDataInfo<BizGoodsVo> queryPageList(BizGoodsBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的商品列表
     *
     * @param bo 查询条件
     * @return 商品列表
     */
    List<BizGoodsVo> queryList(BizGoodsBo bo);

    /**
     * 新增商品
     *
     * @param bo 商品
     * @return 是否新增成功
     */
    Boolean insertByBo(BizGoodsBo bo);

    /**
     * 修改商品
     *
     * @param bo 商品
     * @return 是否修改成功
     */
    Boolean updateByBo(BizGoodsBo bo);

    /**
     * 校验并批量删除商品信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
