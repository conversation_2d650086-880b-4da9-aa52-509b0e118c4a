package com.xu.biz.domain.bo;

import com.xu.biz.domain.BizPlayStrategyMediaRelation;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 播放策略媒体关联业务对象 biz_play_strategy_media_relation
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BizPlayStrategyMediaRelation.class, reverseConvertGenerate = false)
public class BizPlayStrategyMediaRelationBo extends BaseEntity {

    /**
     * 策略ID
     */
    @NotNull(message = "策略ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long strategyId;

    /**
     * 媒体ID
     */
    @NotNull(message = "媒体ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long mediaId;

    /**
     * 顺序
     */
    private Integer sort;


}
