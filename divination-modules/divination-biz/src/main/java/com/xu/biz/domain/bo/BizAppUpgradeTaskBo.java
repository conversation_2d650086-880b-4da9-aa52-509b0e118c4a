package com.xu.biz.domain.bo;

import com.xu.biz.domain.BizAppUpgradeTask;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 升级计划业务对象 biz_app_upgrade_task
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BizAppUpgradeTask.class, reverseConvertGenerate = false)
public class BizAppUpgradeTaskBo extends BaseEntity {

    /**
     * 任务ID
     */
    @NotNull(message = "任务ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 任务名称
     */
    @NotBlank(message = "任务名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 安装包ID
     */
    @NotNull(message = "安装包ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long packageId;

    /**
     * 计划推送时间
     */
    private Date planPushTime;

    /**
     * 实际推送时间
     */
    private Date actualPushTime;

    /**
     * 是否定时推送(0否 1是)
     */
    @NotNull(message = "是否定时推送(0否 1是)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long timelyPush;

    /**
     * 是否静默安装(0-否 1-是)
     */
    @NotNull(message = "是否静默安装(0-否 1-是)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long silentInstall;

    /**
     * 安装模式(IMMEDIATE-立即,IDLE-空闲时,NIGHT-夜间)
     */
    @NotBlank(message = "安装模式(IMMEDIATE-立即,IDLE-空闲时,NIGHT-夜间)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String installModel;


}
