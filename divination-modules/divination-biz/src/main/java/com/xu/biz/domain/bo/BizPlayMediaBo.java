package com.xu.biz.domain.bo;

import com.xu.biz.domain.BizPlayMedia;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 播放媒体业务对象 biz_play_media
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BizPlayMedia.class, reverseConvertGenerate = false)
public class BizPlayMediaBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 描述
     */
    private String remark;

    /**
     * 节假日类型（字典code）
     */
    private String  holidayType;

    /**
     * 播放类型
     */
    @NotBlank(message = "播放类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String playStyle;

    /**
     * 文件ID
     */
    @NotNull(message = "文件ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long fileId;

    /**
     * 文件路径
     */
    private String filePath;


}
