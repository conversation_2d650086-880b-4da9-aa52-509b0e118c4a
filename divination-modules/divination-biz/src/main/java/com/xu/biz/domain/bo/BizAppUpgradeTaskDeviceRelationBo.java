package com.xu.biz.domain.bo;

import com.xu.biz.domain.BizAppUpgradeTaskDeviceRelation;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备商品关联业务对象 biz_app_upgrade_task_device_relation
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BizAppUpgradeTaskDeviceRelation.class, reverseConvertGenerate = false)
public class BizAppUpgradeTaskDeviceRelationBo extends BaseEntity {

    /**
     * 设备ID
     */
    @NotNull(message = "设备ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long deviceId;

    /**
     * 升级任务ID
     */
    @NotNull(message = "升级任务ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long taskId;


}
