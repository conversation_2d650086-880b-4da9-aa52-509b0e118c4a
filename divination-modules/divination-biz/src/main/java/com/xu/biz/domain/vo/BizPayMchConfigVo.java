package com.xu.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xu.biz.domain.BizPayMchConfig;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;



/**
 * 支付商户管理视图对象 biz_pay_mch_config
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BizPayMchConfig.class)
public class BizPayMchConfigVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 关联的服务商配置ID
     */
    @ExcelProperty(value = "关联的服务商配置ID")
    private Long configId;

    /**
     * 子商户号([支付宝]特约商户PID或[微信]特约商户号)
     */
    @ExcelProperty(value = "子商户号([支付宝]特约商户PID或[微信]特约商户号)")
    private String subMchId;

    /**
     * 子商户应用ID([微信]特约商户APPID，部分场景需要)
     */
    @ExcelProperty(value = "子商户应用ID([微信]特约商户APPID，部分场景需要)")
    private String subAppId;

    /**
     * 业务类型(用于区分同一商户不同业务)
     */
    @ExcelProperty(value = "业务类型(用于区分同一商户不同业务)")
    private String businessType;

    /**
     * 结算费率
     */
    @ExcelProperty(value = "结算费率")
    private Long rate;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序")
    private Integer sort;

    /**
     * 状态:0-禁用,1-启用
     */
    @ExcelProperty(value = "状态:0-禁用,1-启用")
    private Integer status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
