package com.xu.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xu.biz.domain.BizPlayStrategyMediaRelation;
import com.xu.biz.domain.bo.BizPlayStrategyMediaRelationBo;
import com.xu.biz.domain.vo.BizPlayStrategyMediaRelationVo;
import com.xu.biz.mapper.BizPlayStrategyMediaRelationMapper;
import com.xu.biz.service.IBizPlayStrategyMediaRelationService;
import com.xu.common.core.utils.MapstructUtils;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 播放策略媒体关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RequiredArgsConstructor
@Service
public class BizPlayStrategyMediaRelationServiceImpl implements IBizPlayStrategyMediaRelationService {

    private final BizPlayStrategyMediaRelationMapper baseMapper;

    /**
     * 查询播放策略媒体关联
     *
     * @param strategyId 主键
     * @return 播放策略媒体关联
     */
    @Override
    public BizPlayStrategyMediaRelationVo queryById(Long strategyId){
        return baseMapper.selectVoById(strategyId);
    }

    /**
     * 分页查询播放策略媒体关联列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 播放策略媒体关联分页列表
     */
    @Override
    public TableDataInfo<BizPlayStrategyMediaRelationVo> queryPageList(BizPlayStrategyMediaRelationBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BizPlayStrategyMediaRelation> lqw = buildQueryWrapper(bo);
        Page<BizPlayStrategyMediaRelationVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的播放策略媒体关联列表
     *
     * @param bo 查询条件
     * @return 播放策略媒体关联列表
     */
    @Override
    public List<BizPlayStrategyMediaRelationVo> queryList(BizPlayStrategyMediaRelationBo bo) {
        LambdaQueryWrapper<BizPlayStrategyMediaRelation> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BizPlayStrategyMediaRelation> buildQueryWrapper(BizPlayStrategyMediaRelationBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BizPlayStrategyMediaRelation> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStrategyId() != null, BizPlayStrategyMediaRelation::getStrategyId, bo.getStrategyId());
        lqw.eq(bo.getMediaId() != null, BizPlayStrategyMediaRelation::getMediaId, bo.getMediaId());
        lqw.eq(bo.getSort() != null, BizPlayStrategyMediaRelation::getSort, bo.getSort());
        return lqw;
    }

    /**
     * 新增播放策略媒体关联
     *
     * @param bo 播放策略媒体关联
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BizPlayStrategyMediaRelationBo bo) {
        BizPlayStrategyMediaRelation add = MapstructUtils.convert(bo, BizPlayStrategyMediaRelation.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setStrategyId(add.getStrategyId());
        }
        return flag;
    }

    /**
     * 修改播放策略媒体关联
     *
     * @param bo 播放策略媒体关联
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BizPlayStrategyMediaRelationBo bo) {
        BizPlayStrategyMediaRelation update = MapstructUtils.convert(bo, BizPlayStrategyMediaRelation.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BizPlayStrategyMediaRelation entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除播放策略媒体关联信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
