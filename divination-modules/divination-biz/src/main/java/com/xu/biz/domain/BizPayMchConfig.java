package com.xu.biz.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xu.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 支付商户管理对象 biz_pay_mch_config
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_pay_mch_config")
public class BizPayMchConfig extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 关联的服务商配置ID
     */
    private Long configId;

    /**
     * 子商户号([支付宝]特约商户PID或[微信]特约商户号)
     */
    private String subMchId;

    /**
     * 子商户应用ID([微信]特约商户APPID，部分场景需要)
     */
    private String subAppId;

    /**
     * 业务类型(用于区分同一商户不同业务)
     */
    private String businessType;

    /**
     * 结算费率
     */
    private Long rate;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态:0-禁用,1-启用
     */
    private Integer status;

    /**
     * 删除标识 0否 1是
     */
    @TableLogic
    private Long delFlag;

    /**
     * 备注
     */
    private String remark;


}
