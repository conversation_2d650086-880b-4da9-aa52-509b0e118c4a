package com.xu.biz.domain.bo;

import com.xu.biz.domain.BizContractRentRecord;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 合同租金记录业务对象 biz_contract_rent_record
 *
 * <AUTHOR>
 * @date 2025-05-23
 * @deprecated 此类已废弃，请使用新的实现
 */
@Deprecated
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BizContractRentRecord.class, reverseConvertGenerate = false)
public class BizContractRentRecordBo extends BaseEntity {

    /**
     * 记录ID
     */
    @NotNull(message = "记录ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 合同ID
     */
    @NotNull(message = "合同ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long contractId;

    /**
     * 缴纳人员
     */
    private String payPerson;

    /**
     * 缴纳时间
     */
    private Date payTime;

    /**
     * 缴纳金额
     */
    @NotNull(message = "缴纳金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long payAmount;

    /**
     * 备注
     */
    private Date remark;


}
