package com.xu.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xu.biz.domain.BizDeviceOperRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 设备操作记录视图对象 biz_device_oper_record
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BizDeviceOperRecord.class)
public class BizDeviceOperRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    private Long id;

    /**
     * 设备ID
     */
    @ExcelProperty(value = "设备ID")
    private Long deviceId;

    /**
     * 设备名称
     */
    @ExcelProperty(value = "设备名称")
    private String deviceName;

    /**
     * 操作类型（REBOOT重启/CLEAN清除缓存/ENFORCE强制出签）
     */
    @ExcelProperty(value = "操作类型")
    private String operType;

    /**
     * 操作时间
     */
    @ExcelProperty(value = "操作时间")
    private Date operTime;

    /**
     * 推送内容
     */
    @ExcelProperty(value = "推送内容")
    private String pushContent;

    /**
     * 推送批次号
     */
    @ExcelProperty(value = "推送批次号")
    private String pushBatchNo;


}
