package com.xu.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xu.biz.domain.BizGoods;
import com.xu.biz.domain.bo.BizGoodsBo;
import com.xu.biz.domain.vo.BizGoodsVo;
import com.xu.biz.mapper.BizGoodsMapper;
import com.xu.biz.service.IBizGoodsService;
import com.xu.common.core.utils.MapstructUtils;
import com.xu.common.core.utils.StringUtils;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 商品Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RequiredArgsConstructor
@Service
public class BizGoodsServiceImpl implements IBizGoodsService {

    private final BizGoodsMapper baseMapper;

    /**
     * 查询商品
     *
     * @param id 主键
     * @return 商品
     */
    @Override
    public BizGoodsVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询商品列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 商品分页列表
     */
    @Override
    public TableDataInfo<BizGoodsVo> queryPageList(BizGoodsBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BizGoods> lqw = buildQueryWrapper(bo);
        Page<BizGoodsVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的商品列表
     *
     * @param bo 查询条件
     * @return 商品列表
     */
    @Override
    public List<BizGoodsVo> queryList(BizGoodsBo bo) {
        LambdaQueryWrapper<BizGoods> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BizGoods> buildQueryWrapper(BizGoodsBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BizGoods> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(BizGoods::getId);
        lqw.like(StringUtils.isNotBlank(bo.getName()), BizGoods::getName, bo.getName());
        lqw.eq(bo.getType() != null, BizGoods::getType, bo.getType());
        lqw.eq(bo.getFileId() != null, BizGoods::getFileId, bo.getFileId());
        lqw.eq(StringUtils.isNotBlank(bo.getFilePath()), BizGoods::getFilePath, bo.getFilePath());
        lqw.eq(bo.getPrice() != null, BizGoods::getPrice, bo.getPrice());
        return lqw;
    }

    /**
     * 新增商品
     *
     * @param bo 商品
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BizGoodsBo bo) {
        BizGoods add = MapstructUtils.convert(bo, BizGoods.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改商品
     *
     * @param bo 商品
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BizGoodsBo bo) {
        BizGoods update = MapstructUtils.convert(bo, BizGoods.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BizGoods entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除商品信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
