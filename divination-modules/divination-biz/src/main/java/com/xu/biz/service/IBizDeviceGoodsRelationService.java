package com.xu.biz.service;


import com.xu.biz.domain.bo.BizDeviceGoodsRelationBo;
import com.xu.biz.domain.vo.BizDeviceGoodsRelationVo;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 设备商品关联Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IBizDeviceGoodsRelationService {

    /**
     * 查询设备商品关联
     *
     * @param deviceId 主键
     * @return 设备商品关联
     */
    BizDeviceGoodsRelationVo queryById(Long deviceId);

    /**
     * 分页查询设备商品关联列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 设备商品关联分页列表
     */
    TableDataInfo<BizDeviceGoodsRelationVo> queryPageList(BizDeviceGoodsRelationBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的设备商品关联列表
     *
     * @param bo 查询条件
     * @return 设备商品关联列表
     */
    List<BizDeviceGoodsRelationVo> queryList(BizDeviceGoodsRelationBo bo);

    /**
     * 新增设备商品关联
     *
     * @param bo 设备商品关联
     * @return 是否新增成功
     */
    Boolean insertByBo(BizDeviceGoodsRelationBo bo);

    /**
     * 修改设备商品关联
     *
     * @param bo 设备商品关联
     * @return 是否修改成功
     */
    Boolean updateByBo(BizDeviceGoodsRelationBo bo);

    /**
     * 校验并批量删除设备商品关联信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
