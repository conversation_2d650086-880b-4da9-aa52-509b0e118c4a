package com.xu.biz.service;


import com.xu.biz.domain.bo.BizContractRentRecordBo;
import com.xu.biz.domain.vo.BizContractRentRecordVo;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 合同租金记录Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 * @deprecated 此接口已废弃，请使用新的实现
 */
@Deprecated
public interface IBizContractRentRecordService {

    /**
     * 查询合同租金记录
     *
     * @param id 主键
     * @return 合同租金记录
     */
    BizContractRentRecordVo queryById(Long id);

    /**
     * 分页查询合同租金记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 合同租金记录分页列表
     */
    TableDataInfo<BizContractRentRecordVo> queryPageList(BizContractRentRecordBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的合同租金记录列表
     *
     * @param bo 查询条件
     * @return 合同租金记录列表
     */
    List<BizContractRentRecordVo> queryList(BizContractRentRecordBo bo);

    /**
     * 新增合同租金记录
     *
     * @param bo 合同租金记录
     * @return 是否新增成功
     */
    Boolean insertByBo(BizContractRentRecordBo bo);

    /**
     * 修改合同租金记录
     *
     * @param bo 合同租金记录
     * @return 是否修改成功
     */
    Boolean updateByBo(BizContractRentRecordBo bo);

    /**
     * 校验并批量删除合同租金记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
