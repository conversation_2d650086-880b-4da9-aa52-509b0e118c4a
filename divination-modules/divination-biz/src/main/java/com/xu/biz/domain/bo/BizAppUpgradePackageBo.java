package com.xu.biz.domain.bo;

import com.xu.biz.domain.BizAppUpgradePackage;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 安装包管理业务对象 biz_app_upgrade_package
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BizAppUpgradePackage.class, reverseConvertGenerate = false)
public class BizAppUpgradePackageBo extends BaseEntity {

    /**
     * 安装包ID
     */
    @NotNull(message = "安装包ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 安装包名称
     */
    private String name;

    /**
     * 版本号(整数)
     */
    @NotNull(message = "版本号(整数)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long code;

    /**
     * 版本编号(如1.0.0)
     */
    @NotBlank(message = "版本编号(如1.0.0)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String number;

    /**
     * 文件ID
     */
    @NotBlank(message = "文件ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fileId;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 版本说明
     */
    private String releaseNotes;

    /**
     * 是否强制更新(0-否 1-是)
     */
    @NotNull(message = "是否强制更新(0-否 1-是)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long forceUpdate;


}
