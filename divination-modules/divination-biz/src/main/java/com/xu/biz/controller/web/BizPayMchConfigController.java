package com.xu.biz.controller.web;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.xu.biz.domain.bo.BizPayMchConfigBo;
import com.xu.biz.domain.vo.BizPayMchConfigVo;
import com.xu.biz.service.IBizPayMchConfigService;
import com.xu.common.core.domain.R;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.excel.utils.ExcelUtil;
import com.xu.common.idempotent.annotation.RepeatSubmit;
import com.xu.common.log.annotation.Log;
import com.xu.common.log.enums.BusinessType;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import com.xu.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
/**
 * 支付商户管理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/biz/payMchConfig")
public class BizPayMchConfigController extends BaseController {

    private final IBizPayMchConfigService bizPayMchConfigService;

    /**
     * 查询支付商户管理列表
     */
    @SaCheckPermission("biz:payMchConfig:list")
    @GetMapping("/list")
    public TableDataInfo<BizPayMchConfigVo> list(BizPayMchConfigBo bo, PageQuery pageQuery) {
        return bizPayMchConfigService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出支付商户管理列表
     */
    @SaCheckPermission("biz:payMchConfig:export")
    @Log(title = "支付商户管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BizPayMchConfigBo bo, HttpServletResponse response) {
        List<BizPayMchConfigVo> list = bizPayMchConfigService.queryList(bo);
        ExcelUtil.exportExcel(list, "支付商户管理", BizPayMchConfigVo.class, response);
    }

    /**
     * 获取支付商户管理详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:payMchConfig:query")
    @GetMapping("/{id}")
    public R<BizPayMchConfigVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(bizPayMchConfigService.queryById(id));
    }

    /**
     * 新增支付商户管理
     */
    @SaCheckPermission("biz:payMchConfig:add")
    @Log(title = "支付商户管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BizPayMchConfigBo bo) {
        return toAjax(bizPayMchConfigService.insertByBo(bo));
    }

    /**
     * 修改支付商户管理
     */
    @SaCheckPermission("biz:payMchConfig:edit")
    @Log(title = "支付商户管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BizPayMchConfigBo bo) {
        return toAjax(bizPayMchConfigService.updateByBo(bo));
    }

    /**
     * 删除支付商户管理
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:payMchConfig:remove")
    @Log(title = "支付商户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(bizPayMchConfigService.deleteWithValidByIds(List.of(ids), true));
    }
}
