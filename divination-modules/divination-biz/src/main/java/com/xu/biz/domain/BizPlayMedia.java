package com.xu.biz.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xu.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 播放媒体对象 biz_play_media
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_play_media")
public class BizPlayMedia extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 描述
     */
    private String remark;

    /**
     * 节假日类型（字典code）
     */
    private String holidayType;

    /**
     * 播放类型
     */
    private String playStyle;

    /**
     * 文件ID
     */
    private Long fileId;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 删除标识 0否 1是
     */
    @TableLogic
    private Long delFlag;


}
