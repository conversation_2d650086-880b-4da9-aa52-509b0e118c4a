package com.xu.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xu.biz.domain.BizDeviceType;
import com.xu.biz.domain.bo.BizDeviceTypeBo;
import com.xu.biz.domain.vo.BizDeviceTypeVo;
import com.xu.biz.mapper.BizDeviceTypeMapper;
import com.xu.biz.service.IBizDeviceTypeService;
import com.xu.common.core.utils.MapstructUtils;
import com.xu.common.core.utils.StringUtils;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 设备类型Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RequiredArgsConstructor
@Service
public class BizDeviceTypeServiceImpl implements IBizDeviceTypeService {

    private final BizDeviceTypeMapper baseMapper;

    /**
     * 查询设备类型
     *
     * @param id 主键
     * @return 设备类型
     */
    @Override
    public BizDeviceTypeVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询设备类型列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 设备类型分页列表
     */
    @Override
    public TableDataInfo<BizDeviceTypeVo> queryPageList(BizDeviceTypeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BizDeviceType> lqw = buildQueryWrapper(bo);
        Page<BizDeviceTypeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的设备类型列表
     *
     * @param bo 查询条件
     * @return 设备类型列表
     */
    @Override
    public List<BizDeviceTypeVo> queryList(BizDeviceTypeBo bo) {
        LambdaQueryWrapper<BizDeviceType> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BizDeviceType> buildQueryWrapper(BizDeviceTypeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BizDeviceType> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(BizDeviceType::getId);
        lqw.like(StringUtils.isNotBlank(bo.getName()), BizDeviceType::getName, bo.getName());
        return lqw;
    }

    /**
     * 新增设备类型
     *
     * @param bo 设备类型
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BizDeviceTypeBo bo) {
        BizDeviceType add = MapstructUtils.convert(bo, BizDeviceType.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改设备类型
     *
     * @param bo 设备类型
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BizDeviceTypeBo bo) {
        BizDeviceType update = MapstructUtils.convert(bo, BizDeviceType.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BizDeviceType entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除设备类型信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
