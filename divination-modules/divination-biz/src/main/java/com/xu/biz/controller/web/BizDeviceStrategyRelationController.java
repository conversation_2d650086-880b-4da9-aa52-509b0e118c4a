package com.xu.biz.controller.web;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.xu.biz.domain.bo.BizDeviceStrategyRelationBo;
import com.xu.biz.domain.vo.BizDeviceStrategyRelationVo;
import com.xu.biz.service.IBizDeviceStrategyRelationService;
import com.xu.common.core.domain.R;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.excel.utils.ExcelUtil;
import com.xu.common.idempotent.annotation.RepeatSubmit;
import com.xu.common.log.annotation.Log;
import com.xu.common.log.enums.BusinessType;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import com.xu.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
/**
 * 设备播放策略关联
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/biz/deviceStrategyRelation")
public class BizDeviceStrategyRelationController extends BaseController {

    private final IBizDeviceStrategyRelationService bizDeviceStrategyRelationService;

    /**
     * 查询设备播放策略关联列表
     */
    @SaCheckPermission("biz:deviceStrategyRelation:list")
    @GetMapping("/list")
    public TableDataInfo<BizDeviceStrategyRelationVo> list(BizDeviceStrategyRelationBo bo, PageQuery pageQuery) {
        return bizDeviceStrategyRelationService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出设备播放策略关联列表
     */
    @SaCheckPermission("biz:deviceStrategyRelation:export")
    @Log(title = "设备播放策略关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BizDeviceStrategyRelationBo bo, HttpServletResponse response) {
        List<BizDeviceStrategyRelationVo> list = bizDeviceStrategyRelationService.queryList(bo);
        ExcelUtil.exportExcel(list, "设备播放策略关联", BizDeviceStrategyRelationVo.class, response);
    }

    /**
     * 获取设备播放策略关联详细信息
     *
     * @param deviceId 主键
     */
    @SaCheckPermission("biz:deviceStrategyRelation:query")
    @GetMapping("/{deviceId}")
    public R<BizDeviceStrategyRelationVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long deviceId) {
        return R.ok(bizDeviceStrategyRelationService.queryById(deviceId));
    }

    /**
     * 新增设备播放策略关联
     */
    @SaCheckPermission("biz:deviceStrategyRelation:add")
    @Log(title = "设备播放策略关联", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BizDeviceStrategyRelationBo bo) {
        return toAjax(bizDeviceStrategyRelationService.insertByBo(bo));
    }

    /**
     * 修改设备播放策略关联
     */
    @SaCheckPermission("biz:deviceStrategyRelation:edit")
    @Log(title = "设备播放策略关联", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BizDeviceStrategyRelationBo bo) {
        return toAjax(bizDeviceStrategyRelationService.updateByBo(bo));
    }

    /**
     * 删除设备播放策略关联
     *
     * @param deviceIds 主键串
     */
    @SaCheckPermission("biz:deviceStrategyRelation:remove")
    @Log(title = "设备播放策略关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deviceIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] deviceIds) {
        return toAjax(bizDeviceStrategyRelationService.deleteWithValidByIds(List.of(deviceIds), true));
    }
}
