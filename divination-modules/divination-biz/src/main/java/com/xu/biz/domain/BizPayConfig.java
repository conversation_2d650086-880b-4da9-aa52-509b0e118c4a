package com.xu.biz.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xu.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 支付配置对象 biz_pay_config
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_pay_config")
public class BizPayConfig extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 配置名称
     */
    private String configName;

    /**
     * 支付类型: ALIPAY(支付宝)/WXPAY(微信支付)
     */
    private String payType;

    /**
     * 应用ID(支付宝APPID或微信APPID)
     */
    private String appId;

    /**
     * 商户号(支付宝PID或微信支付商户号)
     */
    private String mchId;

    /**
     * 回调域名(支付宝和微信共用)
     */
    private String domain;

    /**
     * 默认回调地址(支付宝和微信共用)
     */
    private String notifyUrl;

    /**
     * [支付宝] 应用私钥
     */
    private String aliPrivateKey;

    /**
     * [支付宝] 支付宝公钥(普通公钥方式)
     */
    private String aliPublicKey;

    /**
     * [支付宝] 应用证书路径(证书模式)
     */
    private String aliAppCertPath;

    /**
     * [支付宝] 支付宝公钥证书路径(证书模式)
     */
    private String aliPayCertPath;

    /**
     * [支付宝] 支付宝根证书路径(证书模式)
     */
    private String aliPayRootCertPath;

    /**
     * [支付宝] 网关URL(正式:https://openapi.alipay.com/gateway.do 沙箱:https://openapi.alipaydev.com/gateway.do)
     */
    private String aliServiceUrl;

    /**
     * [支付宝] 是否使用证书模式:0-普通公钥,1-证书模式
     */
    private Long aliCertModel;

    /**
     * [微信] API密钥(V2接口)
     */
    private String wxApiKeyV2;

    /**
     * [微信] API v3密钥(V3接口)
     */
    private String wxApiKeyV3;

    /**
     * [微信] API证书路径(cert.pem)
     */
    private String wxCertPath;

    /**
     * [微信] API证书p12路径(apiclient_cert.p12)
     */
    private String wxCertP12Path;

    /**
     * [微信] API证书密钥路径(key.pem)
     */
    private String wxKeyPath;

    /**
     * [微信] 平台证书路径(V3接口)
     */
    private String wxPlatformCertPath;

    /**
     * 状态:0-禁用,1-启用
     */
    private Integer status;

    /**
     * 删除标识 0否 1是
     */
    @TableLogic
    private Long delFlag;

    /**
     * 备注
     */
    private String remark;


}
