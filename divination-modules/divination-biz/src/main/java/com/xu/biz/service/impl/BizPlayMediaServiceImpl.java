package com.xu.biz.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xu.biz.domain.BizPlayMedia;
import com.xu.biz.domain.bo.BizPlayMediaBo;
import com.xu.biz.domain.vo.BizPlayMediaVo;
import com.xu.biz.mapper.BizPlayMediaMapper;
import com.xu.biz.service.IBizPlayMediaService;
import com.xu.common.core.utils.MapstructUtils;
import com.xu.common.core.utils.StringUtils;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 播放媒体Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RequiredArgsConstructor
@Service
public class BizPlayMediaServiceImpl implements IBizPlayMediaService {

    private final BizPlayMediaMapper baseMapper;

    /**
     * 查询播放媒体
     *
     * @param id 主键
     * @return 播放媒体
     */
    @Override
    public BizPlayMediaVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询播放媒体列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 播放媒体分页列表
     */
    @Override
    public TableDataInfo<BizPlayMediaVo> queryPageList(BizPlayMediaBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BizPlayMedia> lqw = buildQueryWrapper(bo);
        Page<BizPlayMediaVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的播放媒体列表
     *
     * @param bo 查询条件
     * @return 播放媒体列表
     */
    @Override
    public List<BizPlayMediaVo> queryList(BizPlayMediaBo bo) {
        LambdaQueryWrapper<BizPlayMedia> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BizPlayMedia> buildQueryWrapper(BizPlayMediaBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BizPlayMedia> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(BizPlayMedia::getId);
        lqw.like(StringUtils.isNotBlank(bo.getName()), BizPlayMedia::getName, bo.getName());
        lqw.eq(StrUtil.isNotBlank(bo.getHolidayType()), BizPlayMedia::getHolidayType, bo.getHolidayType());
        lqw.eq(StringUtils.isNotBlank(bo.getPlayStyle()), BizPlayMedia::getPlayStyle, bo.getPlayStyle());
        lqw.eq(bo.getFileId() != null, BizPlayMedia::getFileId, bo.getFileId());
        lqw.eq(StringUtils.isNotBlank(bo.getFilePath()), BizPlayMedia::getFilePath, bo.getFilePath());
        return lqw;
    }

    /**
     * 新增播放媒体
     *
     * @param bo 播放媒体
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BizPlayMediaBo bo) {
        BizPlayMedia add = MapstructUtils.convert(bo, BizPlayMedia.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改播放媒体
     *
     * @param bo 播放媒体
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BizPlayMediaBo bo) {
        BizPlayMedia update = MapstructUtils.convert(bo, BizPlayMedia.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BizPlayMedia entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除播放媒体信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
