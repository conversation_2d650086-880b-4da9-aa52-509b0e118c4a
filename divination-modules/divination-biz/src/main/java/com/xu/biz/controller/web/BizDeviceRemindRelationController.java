package com.xu.biz.controller.web;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.xu.biz.domain.bo.BizDeviceRemindRelationBo;
import com.xu.biz.domain.vo.BizDeviceRemindRelationVo;
import com.xu.biz.service.IBizDeviceRemindRelationService;
import com.xu.common.core.domain.R;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.excel.utils.ExcelUtil;
import com.xu.common.idempotent.annotation.RepeatSubmit;
import com.xu.common.log.annotation.Log;
import com.xu.common.log.enums.BusinessType;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import com.xu.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
/**
 * 设备提醒关联
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/biz/deviceRemindRelation")
public class BizDeviceRemindRelationController extends BaseController {

    private final IBizDeviceRemindRelationService bizDeviceRemindRelationService;

    /**
     * 查询设备提醒关联列表
     */
    @SaCheckPermission("biz:deviceRemindRelation:list")
    @GetMapping("/list")
    public TableDataInfo<BizDeviceRemindRelationVo> list(BizDeviceRemindRelationBo bo, PageQuery pageQuery) {
        return bizDeviceRemindRelationService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出设备提醒关联列表
     */
    @SaCheckPermission("biz:deviceRemindRelation:export")
    @Log(title = "设备提醒关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BizDeviceRemindRelationBo bo, HttpServletResponse response) {
        List<BizDeviceRemindRelationVo> list = bizDeviceRemindRelationService.queryList(bo);
        ExcelUtil.exportExcel(list, "设备提醒关联", BizDeviceRemindRelationVo.class, response);
    }

    /**
     * 获取设备提醒关联详细信息
     *
     * @param deviceId 主键
     */
    @SaCheckPermission("biz:deviceRemindRelation:query")
    @GetMapping("/{deviceId}")
    public R<BizDeviceRemindRelationVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long deviceId) {
        return R.ok(bizDeviceRemindRelationService.queryById(deviceId));
    }

    /**
     * 新增设备提醒关联
     */
    @SaCheckPermission("biz:deviceRemindRelation:add")
    @Log(title = "设备提醒关联", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BizDeviceRemindRelationBo bo) {
        return toAjax(bizDeviceRemindRelationService.insertByBo(bo));
    }

    /**
     * 修改设备提醒关联
     */
    @SaCheckPermission("biz:deviceRemindRelation:edit")
    @Log(title = "设备提醒关联", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BizDeviceRemindRelationBo bo) {
        return toAjax(bizDeviceRemindRelationService.updateByBo(bo));
    }

    /**
     * 删除设备提醒关联
     *
     * @param deviceIds 主键串
     */
    @SaCheckPermission("biz:deviceRemindRelation:remove")
    @Log(title = "设备提醒关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deviceIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] deviceIds) {
        return toAjax(bizDeviceRemindRelationService.deleteWithValidByIds(List.of(deviceIds), true));
    }
}
