package com.xu.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xu.biz.domain.BizContractRentRecord;
import com.xu.biz.domain.bo.BizContractRentRecordBo;
import com.xu.biz.domain.vo.BizContractRentRecordVo;
import com.xu.biz.mapper.BizContractRentRecordMapper;
import com.xu.biz.service.IBizContractRentRecordService;
import com.xu.common.core.utils.MapstructUtils;
import com.xu.common.core.utils.StringUtils;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 合同租金记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 * @deprecated 此类已废弃，请使用新的实现
 */
@Deprecated
@RequiredArgsConstructor
@Service
public class BizContractRentRecordServiceImpl implements IBizContractRentRecordService {

    private final BizContractRentRecordMapper baseMapper;

    /**
     * 查询合同租金记录
     *
     * @param id 主键
     * @return 合同租金记录
     */
    @Override
    public BizContractRentRecordVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询合同租金记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 合同租金记录分页列表
     */
    @Override
    public TableDataInfo<BizContractRentRecordVo> queryPageList(BizContractRentRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BizContractRentRecord> lqw = buildQueryWrapper(bo);
        Page<BizContractRentRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的合同租金记录列表
     *
     * @param bo 查询条件
     * @return 合同租金记录列表
     */
    @Override
    public List<BizContractRentRecordVo> queryList(BizContractRentRecordBo bo) {
        LambdaQueryWrapper<BizContractRentRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BizContractRentRecord> buildQueryWrapper(BizContractRentRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BizContractRentRecord> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(BizContractRentRecord::getId);
        lqw.eq(bo.getContractId() != null, BizContractRentRecord::getContractId, bo.getContractId());
        lqw.eq(StringUtils.isNotBlank(bo.getPayPerson()), BizContractRentRecord::getPayPerson, bo.getPayPerson());
        lqw.eq(bo.getPayTime() != null, BizContractRentRecord::getPayTime, bo.getPayTime());
        lqw.eq(bo.getPayAmount() != null, BizContractRentRecord::getPayAmount, bo.getPayAmount());
        return lqw;
    }

    /**
     * 新增合同租金记录
     *
     * @param bo 合同租金记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BizContractRentRecordBo bo) {
        BizContractRentRecord add = MapstructUtils.convert(bo, BizContractRentRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改合同租金记录
     *
     * @param bo 合同租金记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BizContractRentRecordBo bo) {
        BizContractRentRecord update = MapstructUtils.convert(bo, BizContractRentRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BizContractRentRecord entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除合同租金记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
