package com.xu.biz.domain.bo;

import com.xu.biz.domain.BizRemindRecord;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 提醒记录业务对象 biz_remind_record
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BizRemindRecord.class, reverseConvertGenerate = false)
public class BizRemindRecordBo extends BaseEntity {

    /**
     * 记录ID
     */
    @NotNull(message = "记录ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 设备ID
     */
    @NotNull(message = "设备ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long deviceId;

    /**
     * 商品ID
     */
    @NotNull(message = "商品ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long goodsId;

    /**
     * 告警类型(1福不足 2签不足)
     */
    @NotNull(message = "告警类型(1福不足 2签不足)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long alarmType;

    /**
     * 提醒类型(SMS短信/EMAIL邮箱)
     */
    @NotBlank(message = "提醒类型(SMS短信/EMAIL邮箱)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remindType;

    /**
     * 提醒时间
     */
    @NotNull(message = "提醒时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date remindTime;

    /**
     * 提醒人
     */
    private String remindPerson;

    /**
     * 提醒内容
     */
    private String remindContent;


}
