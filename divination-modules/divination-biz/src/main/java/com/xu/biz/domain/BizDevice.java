package com.xu.biz.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xu.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 设备基础信息对象 biz_device
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_device")
public class BizDevice extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 设备ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 设备编码
     */
    private String code;

    /**
     * 设备名称
     */
    private String name;

    /**
     * 位置描述
     */
    private String location;

    /**
     * 区域编码
     */
    private String areaCode;

    /**
     * 类型ID
     */
    private Long typeId;

    /**
     * 分组ID
     */
    private Long groupId;

    /**
     * 当地联系人
     */
    private String contact;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 开启状态(0-禁用 1-启用)
     */
    private String opneStatus;

    /**
     * 在线状态(0-离线 1-在线)
     */
    private String onlineStatus;

    /**
     * 维修状态(0-正常 1-损坏 2维修中)
     */
    private String repairStatus;

    /**
     * 设备租金(元/月)
     */
    private Long rent;

    /**
     * 最后心跳时间
     */
    private Date lastHeartbeat;

    /**
     * 设备IP
     */
    private String ip;

    /**
     * MAC地址
     */
    private String mac;

    /**
     * 应用版本号
     */
    private String appNumber;

    /**
     * 删除标识 0否 1是
     */
    @TableLogic
    private Long delFlag;

    /**
     * 常量时间/s
     */
    private Long everBrightTime;

}
