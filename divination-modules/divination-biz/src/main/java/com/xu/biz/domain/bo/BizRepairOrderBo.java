package com.xu.biz.domain.bo;

import com.xu.biz.domain.BizRepairOrder;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 维修工单业务对象 biz_repair_order
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BizRepairOrder.class, reverseConvertGenerate = false)
public class BizRepairOrderBo extends BaseEntity {

    /**
     * 维修单ID
     */
    @NotNull(message = "维修单ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 维修单编号
     */
    private String orderNo;

    /**
     * 终端设备ID
     */
    @NotBlank(message = "终端设备ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String deviceId;

    /**
     * 终端名称
     */
    private String deviceName;

    /**
     * 故障类型[NO_SIGN:不出签,NO_SYMBOL:不出符,NO_QRCODE:不显示二维码,PAY_FAIL:支付失败,OTHER:其他]
     */
    @NotBlank(message = "故障类型[NO_SIGN:不出签,NO_SYMBOL:不出符,NO_QRCODE:不显示二维码,PAY_FAIL:支付失败,OTHER:其他]不能为空", groups = { AddGroup.class, EditGroup.class })
    private String faultType;

    /**
     * 故障详细描述
     */
    private String faultDesc;

    /**
     * 客户姓名
     */
    @NotBlank(message = "客户姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String customerName;

    /**
     * 客户电话
     */
    @NotBlank(message = "客户电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String customerPhone;

    /**
     * 客户地址
     */
    private String customerAddress;

    /**
     * 紧急程度(字典)
     */
    @NotNull(message = "紧急程度不能为空", groups = { AddGroup.class, EditGroup.class })
    private String emergencyLevel;

    /**
     * 报修来源(1-电话报修 2-终端上报 3-平台录入)
     */
    @NotNull(message = "报修来源不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer reportSource;

    /**
     * 维修状态(0-待处理 1-处理中 3-已完成 4-已取消)
     */
    @NotNull(message = "维修状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer status;

    /**
     * 维修人员ID
     */
    private Long repairManId;

    /**
     * 维修人员姓名
     */
    private String repairManName;

    /**
     * 维修人员电话
     */
    private String repairManPhone;

    /**
     * 预约上门时间
     */
    private Date appointmentTime;

    /**
     * 维修开始时间
     */
    private Date startTime;

    /**
     * 维修完成时间
     */
    private Date finishTime;

    /**
     * 处理结果
     */
    private String solution;

    /**
     * 客户反馈(1-满意 2-一般 3-不满意)
     */
    private Long customerFeedback;


}
