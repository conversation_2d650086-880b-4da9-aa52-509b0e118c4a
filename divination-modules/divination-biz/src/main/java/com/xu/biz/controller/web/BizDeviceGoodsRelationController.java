package com.xu.biz.controller.web;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.xu.biz.domain.bo.BizDeviceGoodsRelationBo;
import com.xu.biz.domain.vo.BizDeviceGoodsRelationVo;
import com.xu.biz.service.IBizDeviceGoodsRelationService;
import com.xu.common.core.domain.R;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.excel.utils.ExcelUtil;
import com.xu.common.idempotent.annotation.RepeatSubmit;
import com.xu.common.log.annotation.Log;
import com.xu.common.log.enums.BusinessType;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import com.xu.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
/**
 * 设备商品关联
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/biz/deviceGoodsRelation")
public class BizDeviceGoodsRelationController extends BaseController {

    private final IBizDeviceGoodsRelationService bizDeviceGoodsRelationService;

    /**
     * 查询设备商品关联列表
     */
    @SaCheckPermission("biz:deviceGoodsRelation:list")
    @GetMapping("/list")
    public TableDataInfo<BizDeviceGoodsRelationVo> list(BizDeviceGoodsRelationBo bo, PageQuery pageQuery) {
        return bizDeviceGoodsRelationService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出设备商品关联列表
     */
    @SaCheckPermission("biz:deviceGoodsRelation:export")
    @Log(title = "设备商品关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BizDeviceGoodsRelationBo bo, HttpServletResponse response) {
        List<BizDeviceGoodsRelationVo> list = bizDeviceGoodsRelationService.queryList(bo);
        ExcelUtil.exportExcel(list, "设备商品关联", BizDeviceGoodsRelationVo.class, response);
    }

    /**
     * 获取设备商品关联详细信息
     *
     * @param deviceId 主键
     */
    @SaCheckPermission("biz:deviceGoodsRelation:query")
    @GetMapping("/{deviceId}")
    public R<BizDeviceGoodsRelationVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long deviceId) {
        return R.ok(bizDeviceGoodsRelationService.queryById(deviceId));
    }

    /**
     * 新增设备商品关联
     */
    @SaCheckPermission("biz:deviceGoodsRelation:add")
    @Log(title = "设备商品关联", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BizDeviceGoodsRelationBo bo) {
        return toAjax(bizDeviceGoodsRelationService.insertByBo(bo));
    }

    /**
     * 修改设备商品关联
     */
    @SaCheckPermission("biz:deviceGoodsRelation:edit")
    @Log(title = "设备商品关联", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BizDeviceGoodsRelationBo bo) {
        return toAjax(bizDeviceGoodsRelationService.updateByBo(bo));
    }

    /**
     * 删除设备商品关联
     *
     * @param deviceIds 主键串
     */
    @SaCheckPermission("biz:deviceGoodsRelation:remove")
    @Log(title = "设备商品关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deviceIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] deviceIds) {
        return toAjax(bizDeviceGoodsRelationService.deleteWithValidByIds(List.of(deviceIds), true));
    }
}
