package com.xu.biz.controller.web;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import com.xu.biz.domain.bo.BizSpaceBo;
import com.xu.biz.domain.vo.BizSpaceVo;
import com.xu.biz.service.IBizSpaceService;
import com.xu.common.core.domain.R;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.excel.utils.ExcelUtil;
import com.xu.common.idempotent.annotation.RepeatSubmit;
import com.xu.common.log.annotation.Log;
import com.xu.common.log.enums.BusinessType;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import com.xu.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeUtil;

import java.util.List;

/**
 * 空间管理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/biz/space")
public class BizSpaceController extends BaseController {

    private final IBizSpaceService bizSpaceService;

    /**
     * 查询空间管理列表
     */
    @SaCheckPermission("biz:space:list")
    @GetMapping("/list")
    public TableDataInfo<BizSpaceVo> list(BizSpaceBo bo, PageQuery pageQuery) {
        return bizSpaceService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取空间树形结构
     */
    @SaIgnore
    @GetMapping("/tree")
    public R<List<Tree<Long>>> tree(BizSpaceBo bo) {
        // 获取所有空间列表（不分页）
        List<BizSpaceVo> list = bizSpaceService.queryList(bo);

        // 使用hutool的TreeUtil构建树形结构
        List<Tree<Long>> trees = TreeUtil.build(list, 0L,
            (space, tree) -> {
                tree.setId(space.getId());
                tree.setParentId(space.getParentId());
                tree.setName(space.getName());
                tree.putExtra("code", space.getCode());
                tree.putExtra("remark", space.getRemark());
                tree.putExtra("sort", space.getSort());
            });

        return R.ok(trees);
    }

    /**
     * 导出空间管理列表
     */
    @SaCheckPermission("biz:space:export")
    @Log(title = "空间管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BizSpaceBo bo, HttpServletResponse response) {
        List<BizSpaceVo> list = bizSpaceService.queryList(bo);
        ExcelUtil.exportExcel(list, "空间管理", BizSpaceVo.class, response);
    }

    /**
     * 获取空间管理详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:space:query")
    @GetMapping("/{id}")
    public R<BizSpaceVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(bizSpaceService.queryById(id));
    }

    /**
     * 新增空间管理
     */
    @SaCheckPermission("biz:space:add")
    @Log(title = "空间管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BizSpaceBo bo) {
        return toAjax(bizSpaceService.insertByBo(bo));
    }

    /**
     * 修改空间管理
     */
    @SaCheckPermission("biz:space:edit")
    @Log(title = "空间管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BizSpaceBo bo) {
        return toAjax(bizSpaceService.updateByBo(bo));
    }

    /**
     * 删除空间管理
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:space:remove")
    @Log(title = "空间管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(bizSpaceService.deleteWithValidByIds(List.of(ids), true));
    }
}
