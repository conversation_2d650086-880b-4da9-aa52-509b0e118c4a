package com.xu.biz.controller.web;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.xu.biz.domain.bo.BizContractRentRecordBo;
import com.xu.biz.domain.vo.BizContractRentRecordVo;
import com.xu.biz.service.IBizContractRentRecordService;
import com.xu.common.core.domain.R;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.excel.utils.ExcelUtil;
import com.xu.common.idempotent.annotation.RepeatSubmit;
import com.xu.common.log.annotation.Log;
import com.xu.common.log.enums.BusinessType;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import com.xu.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
/**
 * 合同租金记录
 *
 * <AUTHOR>
 * @date 2025-05-23
 * @deprecated 此控制器已废弃，请使用新的实现
 */
@Deprecated
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/biz/contractRentRecord")
public class BizContractRentRecordController extends BaseController {

    private final IBizContractRentRecordService bizContractRentRecordService;

    /**
     * 查询合同租金记录列表
     */
    @SaCheckPermission("biz:contractRentRecord:list")
    @GetMapping("/list")
    public TableDataInfo<BizContractRentRecordVo> list(BizContractRentRecordBo bo, PageQuery pageQuery) {
        return bizContractRentRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出合同租金记录列表
     */
    @SaCheckPermission("biz:contractRentRecord:export")
    @Log(title = "合同租金记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BizContractRentRecordBo bo, HttpServletResponse response) {
        List<BizContractRentRecordVo> list = bizContractRentRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "合同租金记录", BizContractRentRecordVo.class, response);
    }

    /**
     * 获取合同租金记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:contractRentRecord:query")
    @GetMapping("/{id}")
    public R<BizContractRentRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(bizContractRentRecordService.queryById(id));
    }

    /**
     * 新增合同租金记录
     */
    @SaCheckPermission("biz:contractRentRecord:add")
    @Log(title = "合同租金记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BizContractRentRecordBo bo) {
        return toAjax(bizContractRentRecordService.insertByBo(bo));
    }

    /**
     * 修改合同租金记录
     */
    @SaCheckPermission("biz:contractRentRecord:edit")
    @Log(title = "合同租金记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BizContractRentRecordBo bo) {
        return toAjax(bizContractRentRecordService.updateByBo(bo));
    }

    /**
     * 删除合同租金记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:contractRentRecord:remove")
    @Log(title = "合同租金记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(bizContractRentRecordService.deleteWithValidByIds(List.of(ids), true));
    }
}
