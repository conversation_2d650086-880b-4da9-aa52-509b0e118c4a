package com.xu.biz.controller.web;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.xu.biz.domain.bo.BizDeviceGroupBo;
import com.xu.biz.domain.vo.BizDeviceGroupVo;
import com.xu.biz.service.IBizDeviceGroupService;
import com.xu.common.core.domain.R;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.excel.utils.ExcelUtil;
import com.xu.common.idempotent.annotation.RepeatSubmit;
import com.xu.common.log.annotation.Log;
import com.xu.common.log.enums.BusinessType;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import com.xu.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
/**
 * 设备分组
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/biz/deviceGroup")
public class BizDeviceGroupController extends BaseController {

    private final IBizDeviceGroupService bizDeviceGroupService;

    /**
     * 查询设备分组列表
     */
    @SaCheckPermission("biz:deviceGroup:list")
    @GetMapping("/list")
    public TableDataInfo<BizDeviceGroupVo> list(BizDeviceGroupBo bo, PageQuery pageQuery) {
        return bizDeviceGroupService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询设备分组列表（不分页）
     */
    @SaCheckPermission("biz:deviceGroup:list")
    @GetMapping("/listAll")
    public R<List<BizDeviceGroupVo>> listAll(BizDeviceGroupBo bo) {
        List<BizDeviceGroupVo> list = bizDeviceGroupService.queryList(bo);
        return R.ok(list);
    }

    /**
     * 导出设备分组列表
     */
    @SaCheckPermission("biz:deviceGroup:export")
    @Log(title = "设备分组", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BizDeviceGroupBo bo, HttpServletResponse response) {
        List<BizDeviceGroupVo> list = bizDeviceGroupService.queryList(bo);
        ExcelUtil.exportExcel(list, "设备分组", BizDeviceGroupVo.class, response);
    }

    /**
     * 获取设备分组详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:deviceGroup:query")
    @GetMapping("/{id}")
    public R<BizDeviceGroupVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(bizDeviceGroupService.queryById(id));
    }

    /**
     * 新增设备分组
     */
    @SaCheckPermission("biz:deviceGroup:add")
    @Log(title = "设备分组", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BizDeviceGroupBo bo) {
        return toAjax(bizDeviceGroupService.insertByBo(bo));
    }

    /**
     * 修改设备分组
     */
    @SaCheckPermission("biz:deviceGroup:edit")
    @Log(title = "设备分组", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BizDeviceGroupBo bo) {
        return toAjax(bizDeviceGroupService.updateByBo(bo));
    }

    /**
     * 删除设备分组
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:deviceGroup:remove")
    @Log(title = "设备分组", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(bizDeviceGroupService.deleteWithValidByIds(List.of(ids), true));
    }
}
