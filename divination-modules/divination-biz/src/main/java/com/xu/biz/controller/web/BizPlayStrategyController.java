package com.xu.biz.controller.web;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.lang.Assert;
import com.xu.biz.domain.bo.BizPlayStrategyBo;
import com.xu.biz.domain.vo.BizPlayStrategyVo;
import com.xu.biz.service.IBizPlayStrategyService;
import com.xu.common.core.domain.R;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.excel.utils.ExcelUtil;
import com.xu.common.idempotent.annotation.RepeatSubmit;
import com.xu.common.log.annotation.Log;
import com.xu.common.log.enums.BusinessType;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import com.xu.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 播放策略
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/biz/playStrategy")
public class BizPlayStrategyController extends BaseController {

    private final IBizPlayStrategyService bizPlayStrategyService;

    /**
     * 查询播放策略列表
     */
    @SaCheckPermission("biz:playStrategy:list")
    @GetMapping("/list")
    public TableDataInfo<BizPlayStrategyVo> list(BizPlayStrategyBo bo, PageQuery pageQuery) {
        return bizPlayStrategyService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出播放策略列表
     */
    @SaCheckPermission("biz:playStrategy:export")
    @Log(title = "播放策略", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BizPlayStrategyBo bo, HttpServletResponse response) {
        List<BizPlayStrategyVo> list = bizPlayStrategyService.queryList(bo);
        ExcelUtil.exportExcel(list, "播放策略", BizPlayStrategyVo.class, response);
    }

    /**
     * 获取播放策略详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:playStrategy:query")
    @GetMapping("/{id}")
    public R<BizPlayStrategyVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(bizPlayStrategyService.queryById(id));
    }

    /**
     * 新增播放策略
     */
    @SaCheckPermission("biz:playStrategy:add")
    @Log(title = "播放策略", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BizPlayStrategyBo bo) {
        Map<String, List<Long>> mediaConfig = bo.getMediaConfig();
        Assert.notEmpty(mediaConfig,"未设置播放内容");
        List<Long> collect = mediaConfig.values().stream().flatMap(Collection::stream).toList();
        Assert.notEmpty(collect,"未设置播放内容");
        return toAjax(bizPlayStrategyService.insertByBo(bo));
    }

    /**
     * 修改播放策略
     */
    @SaCheckPermission("biz:playStrategy:edit")
    @Log(title = "播放策略", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BizPlayStrategyBo bo) {
        return toAjax(bizPlayStrategyService.updateByBo(bo));
    }

    /**
     * 删除播放策略
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:playStrategy:remove")
    @Log(title = "播放策略", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(bizPlayStrategyService.deleteWithValidByIds(List.of(ids), true));
    }
}
