package com.xu.biz.domain.bo;

import com.xu.biz.domain.BizDeviceGroupRelation;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备分组关联业务对象 biz_device_group_relation
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@Deprecated
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BizDeviceGroupRelation.class, reverseConvertGenerate = false)
public class BizDeviceGroupRelationBo extends BaseEntity {

    /**
     * 分组ID
     */
    @NotNull(message = "分组ID不能为空", groups = { EditGroup.class })
    private Long groupId;

    /**
     * 设备ID
     */
    @NotNull(message = "设备ID不能为空", groups = { EditGroup.class })
    private Long deviceId;


}
