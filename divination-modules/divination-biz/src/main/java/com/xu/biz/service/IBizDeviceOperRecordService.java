package com.xu.biz.service;


import com.xu.biz.domain.bo.BizDeviceOperRecordBo;
import com.xu.biz.domain.vo.BizDeviceOperRecordVo;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 设备操作记录Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IBizDeviceOperRecordService {

    /**
     * 查询设备操作记录
     *
     * @param id 主键
     * @return 设备操作记录
     */
    BizDeviceOperRecordVo queryById(Long id);

    /**
     * 分页查询设备操作记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 设备操作记录分页列表
     */
    TableDataInfo<BizDeviceOperRecordVo> queryPageList(BizDeviceOperRecordBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的设备操作记录列表
     *
     * @param bo 查询条件
     * @return 设备操作记录列表
     */
    List<BizDeviceOperRecordVo> queryList(BizDeviceOperRecordBo bo);

    /**
     * 新增设备操作记录
     *
     * @param bo 设备操作记录
     * @return 是否新增成功
     */
    Boolean insertByBo(BizDeviceOperRecordBo bo);

    /**
     * 修改设备操作记录
     *
     * @param bo 设备操作记录
     * @return 是否修改成功
     */
    Boolean updateByBo(BizDeviceOperRecordBo bo);

    /**
     * 校验并批量删除设备操作记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
