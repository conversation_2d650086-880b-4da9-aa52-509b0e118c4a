package com.xu.biz.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 设备提醒关联对象 biz_device_remind_relation
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@TableName("biz_device_remind_relation")
public class BizDeviceRemindRelation {


    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 提醒方式（SMS短信/EMAIL邮件）
     */
    private Long remindType;

    /**
     * 提醒内容
     */
    private Long remindContent;

    /**
     * 提醒人员
     */
    private String remindPerson;

    /**
     * 是否已经提醒
     */
    private Long reminded;


}
