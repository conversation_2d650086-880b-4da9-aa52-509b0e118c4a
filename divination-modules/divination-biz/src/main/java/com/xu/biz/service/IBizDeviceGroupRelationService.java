package com.xu.biz.service;


import com.xu.biz.domain.bo.BizDeviceGroupRelationBo;
import com.xu.biz.domain.vo.BizDeviceGroupRelationVo;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 设备分组关联Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Deprecated
public interface IBizDeviceGroupRelationService {

    /**
     * 查询设备分组关联
     *
     * @param groupId 主键
     * @return 设备分组关联
     */
    BizDeviceGroupRelationVo queryById(Long groupId);

    /**
     * 分页查询设备分组关联列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 设备分组关联分页列表
     */
    TableDataInfo<BizDeviceGroupRelationVo> queryPageList(BizDeviceGroupRelationBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的设备分组关联列表
     *
     * @param bo 查询条件
     * @return 设备分组关联列表
     */
    List<BizDeviceGroupRelationVo> queryList(BizDeviceGroupRelationBo bo);

    /**
     * 新增设备分组关联
     *
     * @param bo 设备分组关联
     * @return 是否新增成功
     */
    Boolean insertByBo(BizDeviceGroupRelationBo bo);

    /**
     * 修改设备分组关联
     *
     * @param bo 设备分组关联
     * @return 是否修改成功
     */
    Boolean updateByBo(BizDeviceGroupRelationBo bo);

    /**
     * 校验并批量删除设备分组关联信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
