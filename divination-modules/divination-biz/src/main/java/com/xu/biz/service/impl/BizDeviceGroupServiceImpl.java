package com.xu.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xu.biz.domain.BizDeviceGroup;
import com.xu.biz.domain.bo.BizDeviceGroupBo;
import com.xu.biz.domain.vo.BizDeviceGroupVo;
import com.xu.biz.mapper.BizDeviceGroupMapper;
import com.xu.biz.service.IBizDeviceGroupService;
import com.xu.common.core.utils.MapstructUtils;
import com.xu.common.core.utils.StringUtils;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 设备分组Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RequiredArgsConstructor
@Service
public class BizDeviceGroupServiceImpl implements IBizDeviceGroupService {

    private final BizDeviceGroupMapper baseMapper;

    /**
     * 查询设备分组
     *
     * @param id 主键
     * @return 设备分组
     */
    @Override
    public BizDeviceGroupVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询设备分组列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 设备分组分页列表
     */
    @Override
    public TableDataInfo<BizDeviceGroupVo> queryPageList(BizDeviceGroupBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BizDeviceGroup> lqw = buildQueryWrapper(bo);
        Page<BizDeviceGroupVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的设备分组列表
     *
     * @param bo 查询条件
     * @return 设备分组列表
     */
    @Override
    public List<BizDeviceGroupVo> queryList(BizDeviceGroupBo bo) {
        LambdaQueryWrapper<BizDeviceGroup> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BizDeviceGroup> buildQueryWrapper(BizDeviceGroupBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BizDeviceGroup> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(BizDeviceGroup::getId);
        lqw.like(StringUtils.isNotBlank(bo.getName()), BizDeviceGroup::getName, bo.getName());
        return lqw;
    }

    /**
     * 新增设备分组
     *
     * @param bo 设备分组
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BizDeviceGroupBo bo) {
        BizDeviceGroup add = MapstructUtils.convert(bo, BizDeviceGroup.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改设备分组
     *
     * @param bo 设备分组
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BizDeviceGroupBo bo) {
        BizDeviceGroup update = MapstructUtils.convert(bo, BizDeviceGroup.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BizDeviceGroup entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除设备分组信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
