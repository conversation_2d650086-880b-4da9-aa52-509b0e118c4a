package com.xu.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xu.biz.domain.BizAppUpgradeTask;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 升级计划视图对象 biz_app_upgrade_task
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BizAppUpgradeTask.class)
public class BizAppUpgradeTaskVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    private Long id;

    /**
     * 任务名称
     */
    @ExcelProperty(value = "任务名称")
    private String name;

    /**
     * 安装包ID
     */
    @ExcelProperty(value = "安装包ID")
    private Long packageId;

    /**
     * 计划推送时间
     */
    @ExcelProperty(value = "计划推送时间")
    private Date planPushTime;

    /**
     * 实际推送时间
     */
    @ExcelProperty(value = "实际推送时间")
    private Date actualPushTime;

    /**
     * 是否定时推送(0否 1是)
     */
    @ExcelProperty(value = "是否定时推送(0否 1是)")
    private Long timelyPush;

    /**
     * 是否静默安装(0-否 1-是)
     */
    @ExcelProperty(value = "是否静默安装(0-否 1-是)")
    private Long silentInstall;

    /**
     * 安装模式(IMMEDIATE-立即,IDLE-空闲时,NIGHT-夜间)
     */
    @ExcelProperty(value = "安装模式(IMMEDIATE-立即,IDLE-空闲时,NIGHT-夜间)")
    private String installModel;


}
