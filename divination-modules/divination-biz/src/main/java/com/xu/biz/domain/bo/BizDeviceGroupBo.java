package com.xu.biz.domain.bo;

import com.xu.biz.domain.BizDeviceGroup;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备分组业务对象 biz_device_group
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BizDeviceGroup.class, reverseConvertGenerate = false)
public class BizDeviceGroupBo extends BaseEntity {

    /**
     * 分组ID
     */
    @NotNull(message = "分组ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 分组名称
     */
    @NotBlank(message = "分组名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 分组描述
     */
    private String remark;


}
