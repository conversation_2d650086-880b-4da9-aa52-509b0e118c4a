package com.xu.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xu.biz.domain.BizPayRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 支付交易记录视图对象 biz_pay_record
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BizPayRecord.class)
public class BizPayRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 支付记录ID
     */
    private Long id;

    /**
     * 商户订单号
     */
    @ExcelProperty(value = "商户订单号")
    private String outTradeNo;

    /**
     * 设备ID
     */
    @ExcelProperty(value = "设备ID")
    private Long deviceId;

    /**
     * 商品ID
     */
    @ExcelProperty(value = "商品ID")
    private Long goodsId;

    /**
     * 支付配置ID
     */
    @ExcelProperty(value = "支付配置ID")
    private Long payConfigId;

    /**
     * 商户配置ID
     */
    @ExcelProperty(value = "商户配置ID")
    private Long mchConfigId;

    /**
     * 商户ID
     */
    @ExcelProperty(value = "商户ID")
    private String mchId;

    /**
     * 应用ID
     */
    @ExcelProperty(value = "应用ID")
    private String appId;

    /**
     * 支付渠道(1-支付宝 2-微信)
     */
    @ExcelProperty(value = "支付渠道(1-支付宝 2-微信)")
    private Long channel;

    /**
     * 订单金额(元)
     */
    @ExcelProperty(value = "订单金额(元)")
    private Long amount;

    /**
     * 实收金额(元)
     */
    @ExcelProperty(value = "实收金额(元)")
    private Long actualAmount;

    /**
     * 货币类型
     */
    @ExcelProperty(value = "货币类型")
    private String currency;

    /**
     * 返回数据
     */
    @ExcelProperty(value = "返回数据")
    private String resultData;

    /**
     * 订单描述
     */
    @ExcelProperty(value = "订单描述")
    private String remark;

    /**
     * 状态(0-待支付 1-支付成功 2-已关闭 3-已退款)
     */
    @ExcelProperty(value = "状态(0-待支付 1-支付成功 2-已关闭 3-已退款)")
    private Integer status;

    /**
     * 付款方用户标识
     */
    @ExcelProperty(value = "付款方用户标识")
    private String payerUid;

    /**
     * 付款方账号
     */
    @ExcelProperty(value = "付款方账号")
    private String payerAccount;

    /**
     * 支付成功时间
     */
    @ExcelProperty(value = "支付成功时间")
    private Date payTime;


}
