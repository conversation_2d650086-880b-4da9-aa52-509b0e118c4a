package com.xu.biz.service;

import com.xu.biz.domain.bo.BizAppUpgradeRecordBo;
import com.xu.biz.domain.vo.BizAppUpgradeRecordVo;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * APP推送记录Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IBizAppUpgradeRecordService {

    /**
     * 查询APP推送记录
     *
     * @param id 主键
     * @return APP推送记录
     */
    BizAppUpgradeRecordVo queryById(Long id);

    /**
     * 分页查询APP推送记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return APP推送记录分页列表
     */
    TableDataInfo<BizAppUpgradeRecordVo> queryPageList(BizAppUpgradeRecordBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的APP推送记录列表
     *
     * @param bo 查询条件
     * @return APP推送记录列表
     */
    List<BizAppUpgradeRecordVo> queryList(BizAppUpgradeRecordBo bo);

    /**
     * 新增APP推送记录
     *
     * @param bo APP推送记录
     * @return 是否新增成功
     */
    Boolean insertByBo(BizAppUpgradeRecordBo bo);

    /**
     * 修改APP推送记录
     *
     * @param bo APP推送记录
     * @return 是否修改成功
     */
    Boolean updateByBo(BizAppUpgradeRecordBo bo);

    /**
     * 校验并批量删除APP推送记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
