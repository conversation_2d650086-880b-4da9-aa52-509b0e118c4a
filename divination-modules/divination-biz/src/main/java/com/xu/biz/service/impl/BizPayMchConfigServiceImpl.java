package com.xu.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xu.biz.domain.BizPayMchConfig;
import com.xu.biz.domain.bo.BizPayMchConfigBo;
import com.xu.biz.domain.vo.BizPayMchConfigVo;
import com.xu.biz.mapper.BizPayMchConfigMapper;
import com.xu.biz.service.IBizPayMchConfigService;
import com.xu.common.core.utils.MapstructUtils;
import com.xu.common.core.utils.StringUtils;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 支付商户管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RequiredArgsConstructor
@Service
public class BizPayMchConfigServiceImpl implements IBizPayMchConfigService {

    private final BizPayMchConfigMapper baseMapper;

    /**
     * 查询支付商户管理
     *
     * @param id 主键
     * @return 支付商户管理
     */
    @Override
    public BizPayMchConfigVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询支付商户管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 支付商户管理分页列表
     */
    @Override
    public TableDataInfo<BizPayMchConfigVo> queryPageList(BizPayMchConfigBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BizPayMchConfig> lqw = buildQueryWrapper(bo);
        Page<BizPayMchConfigVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的支付商户管理列表
     *
     * @param bo 查询条件
     * @return 支付商户管理列表
     */
    @Override
    public List<BizPayMchConfigVo> queryList(BizPayMchConfigBo bo) {
        LambdaQueryWrapper<BizPayMchConfig> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BizPayMchConfig> buildQueryWrapper(BizPayMchConfigBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BizPayMchConfig> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(BizPayMchConfig::getId);
        lqw.eq(bo.getConfigId() != null, BizPayMchConfig::getConfigId, bo.getConfigId());
        lqw.eq(StringUtils.isNotBlank(bo.getSubMchId()), BizPayMchConfig::getSubMchId, bo.getSubMchId());
        lqw.eq(StringUtils.isNotBlank(bo.getSubAppId()), BizPayMchConfig::getSubAppId, bo.getSubAppId());
        lqw.eq(StringUtils.isNotBlank(bo.getBusinessType()), BizPayMchConfig::getBusinessType, bo.getBusinessType());
        lqw.eq(bo.getRate() != null, BizPayMchConfig::getRate, bo.getRate());
        lqw.eq(bo.getSort() != null, BizPayMchConfig::getSort, bo.getSort());
        lqw.eq(bo.getStatus() != null, BizPayMchConfig::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增支付商户管理
     *
     * @param bo 支付商户管理
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BizPayMchConfigBo bo) {
        BizPayMchConfig add = MapstructUtils.convert(bo, BizPayMchConfig.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改支付商户管理
     *
     * @param bo 支付商户管理
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BizPayMchConfigBo bo) {
        BizPayMchConfig update = MapstructUtils.convert(bo, BizPayMchConfig.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BizPayMchConfig entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除支付商户管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
