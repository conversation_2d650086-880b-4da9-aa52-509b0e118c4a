package com.xu.biz.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xu.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 空间管理对象 biz_space
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_space")
public class BizSpace extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 空间名称
     */
    private String name;

    /**
     * 空间编码
     */
    private String code;

    /**
     * 描述
     */
    private String remark;

    /**
     * x坐标
     * @deprecated 此字段已废弃，请使用新的坐标系统
     */
    @Deprecated
    private String x;

    /**
     * y坐标
     * @deprecated 此字段已废弃，请使用新的坐标系统
     */
    @Deprecated
    private String y;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 删除标识 0否 1是
     */
    @TableLogic
    private Long delFlag;


}
