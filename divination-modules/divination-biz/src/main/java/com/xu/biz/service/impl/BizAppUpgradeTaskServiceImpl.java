package com.xu.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xu.biz.domain.BizAppUpgradeTask;
import com.xu.biz.domain.bo.BizAppUpgradeTaskBo;
import com.xu.biz.domain.vo.BizAppUpgradeTaskVo;
import com.xu.biz.mapper.BizAppUpgradeTaskMapper;
import com.xu.biz.service.IBizAppUpgradeTaskService;
import com.xu.common.core.utils.MapstructUtils;
import com.xu.common.core.utils.StringUtils;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 升级计划Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RequiredArgsConstructor
@Service
public class BizAppUpgradeTaskServiceImpl implements IBizAppUpgradeTaskService {

    private final BizAppUpgradeTaskMapper baseMapper;

    /**
     * 查询升级计划
     *
     * @param id 主键
     * @return 升级计划
     */
    @Override
    public BizAppUpgradeTaskVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询升级计划列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 升级计划分页列表
     */
    @Override
    public TableDataInfo<BizAppUpgradeTaskVo> queryPageList(BizAppUpgradeTaskBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BizAppUpgradeTask> lqw = buildQueryWrapper(bo);
        Page<BizAppUpgradeTaskVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的升级计划列表
     *
     * @param bo 查询条件
     * @return 升级计划列表
     */
    @Override
    public List<BizAppUpgradeTaskVo> queryList(BizAppUpgradeTaskBo bo) {
        LambdaQueryWrapper<BizAppUpgradeTask> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BizAppUpgradeTask> buildQueryWrapper(BizAppUpgradeTaskBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BizAppUpgradeTask> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(BizAppUpgradeTask::getId);
        lqw.like(StringUtils.isNotBlank(bo.getName()), BizAppUpgradeTask::getName, bo.getName());
        lqw.eq(bo.getPackageId() != null, BizAppUpgradeTask::getPackageId, bo.getPackageId());
        lqw.eq(bo.getPlanPushTime() != null, BizAppUpgradeTask::getPlanPushTime, bo.getPlanPushTime());
        lqw.eq(bo.getActualPushTime() != null, BizAppUpgradeTask::getActualPushTime, bo.getActualPushTime());
        lqw.eq(bo.getTimelyPush() != null, BizAppUpgradeTask::getTimelyPush, bo.getTimelyPush());
        lqw.eq(bo.getSilentInstall() != null, BizAppUpgradeTask::getSilentInstall, bo.getSilentInstall());
        lqw.eq(StringUtils.isNotBlank(bo.getInstallModel()), BizAppUpgradeTask::getInstallModel, bo.getInstallModel());
        return lqw;
    }

    /**
     * 新增升级计划
     *
     * @param bo 升级计划
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BizAppUpgradeTaskBo bo) {
        BizAppUpgradeTask add = MapstructUtils.convert(bo, BizAppUpgradeTask.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改升级计划
     *
     * @param bo 升级计划
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BizAppUpgradeTaskBo bo) {
        BizAppUpgradeTask update = MapstructUtils.convert(bo, BizAppUpgradeTask.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BizAppUpgradeTask entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除升级计划信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
