package com.xu.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xu.biz.domain.BizPlayStrategyMediaRelation;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;



/**
 * 播放策略媒体关联视图对象 biz_play_strategy_media_relation
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BizPlayStrategyMediaRelation.class)
public class BizPlayStrategyMediaRelationVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 策略ID
     */
    @ExcelProperty(value = "策略ID")
    private Long strategyId;

    /**
     * 媒体ID
     */
    @ExcelProperty(value = "媒体ID")
    private Long mediaId;

    /**
     * 顺序
     */
    @ExcelProperty(value = "顺序")
    private Integer sort;


}
