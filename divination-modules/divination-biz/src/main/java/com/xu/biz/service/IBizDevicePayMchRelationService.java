package com.xu.biz.service;


import com.xu.biz.domain.bo.BizDevicePayMchRelationBo;
import com.xu.biz.domain.vo.BizDevicePayMchRelationVo;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 设备支付商户关联Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IBizDevicePayMchRelationService {

    /**
     * 查询设备支付商户关联
     *
     * @param deviceId 主键
     * @return 设备支付商户关联
     */
    BizDevicePayMchRelationVo queryById(Long deviceId);

    /**
     * 分页查询设备支付商户关联列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 设备支付商户关联分页列表
     */
    TableDataInfo<BizDevicePayMchRelationVo> queryPageList(BizDevicePayMchRelationBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的设备支付商户关联列表
     *
     * @param bo 查询条件
     * @return 设备支付商户关联列表
     */
    List<BizDevicePayMchRelationVo> queryList(BizDevicePayMchRelationBo bo);

    /**
     * 新增设备支付商户关联
     *
     * @param bo 设备支付商户关联
     * @return 是否新增成功
     */
    Boolean insertByBo(BizDevicePayMchRelationBo bo);

    /**
     * 修改设备支付商户关联
     *
     * @param bo 设备支付商户关联
     * @return 是否修改成功
     */
    Boolean updateByBo(BizDevicePayMchRelationBo bo);

    /**
     * 校验并批量删除设备支付商户关联信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
