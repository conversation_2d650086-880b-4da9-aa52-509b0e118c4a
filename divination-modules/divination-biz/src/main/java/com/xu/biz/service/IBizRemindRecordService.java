package com.xu.biz.service;

import com.xu.biz.domain.bo.BizRemindRecordBo;
import com.xu.biz.domain.vo.BizRemindRecordVo;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 提醒记录Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IBizRemindRecordService {

    /**
     * 查询提醒记录
     *
     * @param id 主键
     * @return 提醒记录
     */
    BizRemindRecordVo queryById(Long id);

    /**
     * 分页查询提醒记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 提醒记录分页列表
     */
    TableDataInfo<BizRemindRecordVo> queryPageList(BizRemindRecordBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的提醒记录列表
     *
     * @param bo 查询条件
     * @return 提醒记录列表
     */
    List<BizRemindRecordVo> queryList(BizRemindRecordBo bo);

    /**
     * 新增提醒记录
     *
     * @param bo 提醒记录
     * @return 是否新增成功
     */
    Boolean insertByBo(BizRemindRecordBo bo);

    /**
     * 修改提醒记录
     *
     * @param bo 提醒记录
     * @return 是否修改成功
     */
    Boolean updateByBo(BizRemindRecordBo bo);

    /**
     * 校验并批量删除提醒记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
