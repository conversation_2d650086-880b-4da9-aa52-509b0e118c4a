package com.xu.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xu.biz.domain.BizDevice;
import com.xu.common.excel.annotation.ExcelDictFormat;
import com.xu.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 设备基础信息视图对象 biz_device
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BizDevice.class)
public class BizDeviceVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 设备ID
     */
    private Long id;

    /**
     * 设备编码
     */
    @ExcelProperty(value = "设备编码")
    private String code;

    /**
     * 设备名称
     */
    @ExcelProperty(value = "设备名称")
    private String name;

    /**
     * 位置描述
     */
    private String location;

    /**
     * 区域ID
     */
    private String areaCode;


    /**
     * 所属区域
     */
    @ExcelProperty(value = "所属区域")
    private String areaName;

    /**
     * 类型ID
     */
    private Long typeId;

    /**
     * 设备类型
     */
    @ExcelProperty(value = "设备类型")
    private String typeName;

    /**
     * 分组ID
     */
    private Long groupId;

    /**
     * 设备分组
     */
    @ExcelProperty(value = "设备分组")
    private String groupName;

    /**
     * 商品库存
     */
    @ExcelProperty(value = "商品库存")
    private String inventory;

    /**
     * 当地联系人
     */
    @ExcelProperty(value = "当地联系人")
    private String contact;

    /**
     * 联系电话
     */
    @ExcelProperty(value = "联系电话")
    private String phone;

    /**
     * 开启状态
     */
    @ExcelDictFormat(dictType = "sys_normal_disable")
    @ExcelProperty(value = "开启状态", converter = ExcelDictConvert.class)
    private String opneStatus;

    /**
     * 在线状态
     */
    @ExcelDictFormat(dictType = "online_status")
    @ExcelProperty(value = "在线状态", converter = ExcelDictConvert.class)
    private String onlineStatus;

    /**
     * 维修状态
     */
    @ExcelDictFormat(dictType = "repair_status")
    @ExcelProperty(value = "维修状态", converter = ExcelDictConvert.class)
    private String repairStatus;

    /**
     * 设备租金(元/月)
     */
    @ExcelProperty(value = "设备租金")
    private Long rent;

    /**
     * 最后心跳时间
     */
    @ExcelProperty(value = "最后心跳时间")
    private Date lastHeartbeat;

    /**
     * 设备IP
     */
    @ExcelProperty(value = "设备IP")
    private String ip;

    /**
     * MAC地址
     */
    @ExcelProperty(value = "MAC地址")
    private String mac;

    /**
     * 应用版本号
     */
    @ExcelProperty(value = "应用版本号")
    private String appNumber;

    /**
     * 常量时间/s
     */
    private Long everBrightTime;

    /**
     * 常量时间
     */
    @ExcelProperty(value = "常量时间")
    private String everBrightTimeStr;



}
