package com.xu.biz.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xu.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 商品对象 biz_goods
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_goods")
public class BizGoods extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 商品描述
     */
    private String remark;

    /**
     * 商品类型(字典)
     */
    private Long type;

    /**
     * 商品主图文件ID
     */
    private Long fileId;

    /**
     * 商品主图文件路径
     */
    private String filePath;

    /**
     * 商品单价
     */
    private Long price;

    /**
     * 删除标识 0否 1是
     */
    @TableLogic
    private Long delFlag;


}
