package com.xu.biz.domain.bo;

import com.xu.biz.domain.BizDeviceOperRecord;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 设备操作记录业务对象 biz_device_oper_record
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BizDeviceOperRecord.class, reverseConvertGenerate = false)
public class BizDeviceOperRecordBo extends BaseEntity {

    /**
     * 记录ID
     */
    @NotNull(message = "记录ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 设备ID
     */
    @NotNull(message = "设备ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long deviceId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 操作类型（REBOOT重启/CLEAN清除缓存/ENFORCE强制出签）
     */
    private String operType;

    /**
     * 操作时间
     */
    @NotNull(message = "操作时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date operTime;

    /**
     * 推送内容
     */
    @NotBlank(message = "推送内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String pushContent;

    /**
     * 推送批次号
     */
    private String pushBatchNo;


}
