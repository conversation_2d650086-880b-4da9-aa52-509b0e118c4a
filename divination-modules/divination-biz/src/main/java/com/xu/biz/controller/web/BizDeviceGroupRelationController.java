package com.xu.biz.controller.web;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.xu.biz.domain.bo.BizDeviceGroupRelationBo;
import com.xu.biz.domain.vo.BizDeviceGroupRelationVo;
import com.xu.biz.service.IBizDeviceGroupRelationService;
import com.xu.common.core.domain.R;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.excel.utils.ExcelUtil;
import com.xu.common.idempotent.annotation.RepeatSubmit;
import com.xu.common.log.annotation.Log;
import com.xu.common.log.enums.BusinessType;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import com.xu.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
/**
 * 设备分组关联
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Deprecated
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/biz/deviceGroupRelation")
public class BizDeviceGroupRelationController extends BaseController {

    private final IBizDeviceGroupRelationService bizDeviceGroupRelationService;

    /**
     * 查询设备分组关联列表
     */
    @SaCheckPermission("biz:deviceGroupRelation:list")
    @GetMapping("/list")
    public TableDataInfo<BizDeviceGroupRelationVo> list(BizDeviceGroupRelationBo bo, PageQuery pageQuery) {
        return bizDeviceGroupRelationService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出设备分组关联列表
     */
    @SaCheckPermission("biz:deviceGroupRelation:export")
    @Log(title = "设备分组关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BizDeviceGroupRelationBo bo, HttpServletResponse response) {
        List<BizDeviceGroupRelationVo> list = bizDeviceGroupRelationService.queryList(bo);
        ExcelUtil.exportExcel(list, "设备分组关联", BizDeviceGroupRelationVo.class, response);
    }

    /**
     * 获取设备分组关联详细信息
     *
     * @param groupId 主键
     */
    @SaCheckPermission("biz:deviceGroupRelation:query")
    @GetMapping("/{groupId}")
    public R<BizDeviceGroupRelationVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long groupId) {
        return R.ok(bizDeviceGroupRelationService.queryById(groupId));
    }

    /**
     * 新增设备分组关联
     */
    @SaCheckPermission("biz:deviceGroupRelation:add")
    @Log(title = "设备分组关联", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BizDeviceGroupRelationBo bo) {
        return toAjax(bizDeviceGroupRelationService.insertByBo(bo));
    }

    /**
     * 修改设备分组关联
     */
    @SaCheckPermission("biz:deviceGroupRelation:edit")
    @Log(title = "设备分组关联", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BizDeviceGroupRelationBo bo) {
        return toAjax(bizDeviceGroupRelationService.updateByBo(bo));
    }

    /**
     * 删除设备分组关联
     *
     * @param groupIds 主键串
     */
    @SaCheckPermission("biz:deviceGroupRelation:remove")
    @Log(title = "设备分组关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/{groupIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] groupIds) {
        return toAjax(bizDeviceGroupRelationService.deleteWithValidByIds(List.of(groupIds), true));
    }
}
