package com.xu.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xu.biz.domain.BizDevicePayMchRelation;
import com.xu.biz.domain.bo.BizDevicePayMchRelationBo;
import com.xu.biz.domain.vo.BizDevicePayMchRelationVo;
import com.xu.biz.mapper.BizDevicePayMchRelationMapper;
import com.xu.biz.service.IBizDevicePayMchRelationService;
import com.xu.common.core.utils.MapstructUtils;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 设备支付商户关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RequiredArgsConstructor
@Service
public class BizDevicePayMchRelationServiceImpl implements IBizDevicePayMchRelationService {

    private final BizDevicePayMchRelationMapper baseMapper;

    /**
     * 查询设备支付商户关联
     *
     * @param deviceId 主键
     * @return 设备支付商户关联
     */
    @Override
    public BizDevicePayMchRelationVo queryById(Long deviceId){
        return baseMapper.selectVoById(deviceId);
    }

    /**
     * 分页查询设备支付商户关联列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 设备支付商户关联分页列表
     */
    @Override
    public TableDataInfo<BizDevicePayMchRelationVo> queryPageList(BizDevicePayMchRelationBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BizDevicePayMchRelation> lqw = buildQueryWrapper(bo);
        Page<BizDevicePayMchRelationVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的设备支付商户关联列表
     *
     * @param bo 查询条件
     * @return 设备支付商户关联列表
     */
    @Override
    public List<BizDevicePayMchRelationVo> queryList(BizDevicePayMchRelationBo bo) {
        LambdaQueryWrapper<BizDevicePayMchRelation> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BizDevicePayMchRelation> buildQueryWrapper(BizDevicePayMchRelationBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BizDevicePayMchRelation> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getDeviceId() != null, BizDevicePayMchRelation::getDeviceId, bo.getDeviceId());
        lqw.eq(bo.getPayMchId() != null, BizDevicePayMchRelation::getPayMchId, bo.getPayMchId());
        return lqw;
    }

    /**
     * 新增设备支付商户关联
     *
     * @param bo 设备支付商户关联
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BizDevicePayMchRelationBo bo) {
        BizDevicePayMchRelation add = MapstructUtils.convert(bo, BizDevicePayMchRelation.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setDeviceId(add.getDeviceId());
        }
        return flag;
    }

    /**
     * 修改设备支付商户关联
     *
     * @param bo 设备支付商户关联
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BizDevicePayMchRelationBo bo) {
        BizDevicePayMchRelation update = MapstructUtils.convert(bo, BizDevicePayMchRelation.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BizDevicePayMchRelation entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除设备支付商户关联信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
