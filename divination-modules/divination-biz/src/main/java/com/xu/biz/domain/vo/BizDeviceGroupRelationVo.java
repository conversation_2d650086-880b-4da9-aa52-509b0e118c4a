package com.xu.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xu.biz.domain.BizDeviceGroupRelation;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;



/**
 * 设备分组关联视图对象 biz_device_group_relation
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@Deprecated
@ExcelIgnoreUnannotated
@AutoMapper(target = BizDeviceGroupRelation.class)
public class BizDeviceGroupRelationVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分组ID
     */
    @ExcelProperty(value = "分组ID")
    private Long groupId;

    /**
     * 设备ID
     */
    @ExcelProperty(value = "设备ID")
    private Long deviceId;


}
