package com.xu.biz.controller.web;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.xu.biz.domain.bo.BizAppUpgradeTaskDeviceRelationBo;
import com.xu.biz.domain.vo.BizAppUpgradeTaskDeviceRelationVo;
import com.xu.biz.service.IBizAppUpgradeTaskDeviceRelationService;
import com.xu.common.core.domain.R;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.excel.utils.ExcelUtil;
import com.xu.common.idempotent.annotation.RepeatSubmit;
import com.xu.common.log.annotation.Log;
import com.xu.common.log.enums.BusinessType;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import com.xu.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 设备商品关联
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/biz/appUpgradeTaskDeviceRelation")
public class BizAppUpgradeTaskDeviceRelationController extends BaseController {

    private final IBizAppUpgradeTaskDeviceRelationService bizAppUpgradeTaskDeviceRelationService;

    /**
     * 查询设备商品关联列表
     */
    @SaCheckPermission("biz:appUpgradeTaskDeviceRelation:list")
    @GetMapping("/list")
    public TableDataInfo<BizAppUpgradeTaskDeviceRelationVo> list(BizAppUpgradeTaskDeviceRelationBo bo, PageQuery pageQuery) {
        return bizAppUpgradeTaskDeviceRelationService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出设备商品关联列表
     */
    @SaCheckPermission("biz:appUpgradeTaskDeviceRelation:export")
    @Log(title = "设备商品关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BizAppUpgradeTaskDeviceRelationBo bo, HttpServletResponse response) {
        List<BizAppUpgradeTaskDeviceRelationVo> list = bizAppUpgradeTaskDeviceRelationService.queryList(bo);
        ExcelUtil.exportExcel(list, "设备商品关联", BizAppUpgradeTaskDeviceRelationVo.class, response);
    }

    /**
     * 获取设备商品关联详细信息
     *
     * @param deviceId 主键
     */
    @SaCheckPermission("biz:appUpgradeTaskDeviceRelation:query")
    @GetMapping("/{deviceId}")
    public R<BizAppUpgradeTaskDeviceRelationVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long deviceId) {
        return R.ok(bizAppUpgradeTaskDeviceRelationService.queryById(deviceId));
    }

    /**
     * 新增设备商品关联
     */
    @SaCheckPermission("biz:appUpgradeTaskDeviceRelation:add")
    @Log(title = "设备商品关联", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BizAppUpgradeTaskDeviceRelationBo bo) {
        return toAjax(bizAppUpgradeTaskDeviceRelationService.insertByBo(bo));
    }

    /**
     * 修改设备商品关联
     */
    @SaCheckPermission("biz:appUpgradeTaskDeviceRelation:edit")
    @Log(title = "设备商品关联", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BizAppUpgradeTaskDeviceRelationBo bo) {
        return toAjax(bizAppUpgradeTaskDeviceRelationService.updateByBo(bo));
    }

    /**
     * 删除设备商品关联
     *
     * @param deviceIds 主键串
     */
    @SaCheckPermission("biz:appUpgradeTaskDeviceRelation:remove")
    @Log(title = "设备商品关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deviceIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] deviceIds) {
        return toAjax(bizAppUpgradeTaskDeviceRelationService.deleteWithValidByIds(List.of(deviceIds), true));
    }
}
