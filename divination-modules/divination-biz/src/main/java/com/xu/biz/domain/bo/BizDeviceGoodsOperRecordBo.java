package com.xu.biz.domain.bo;

import com.xu.biz.domain.BizDeviceGoodsOperRecord;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 商品出补货记录业务对象 biz_device_goods_oper_record
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BizDeviceGoodsOperRecord.class, reverseConvertGenerate = false)
public class BizDeviceGoodsOperRecordBo extends BaseEntity {

    /**
     * 配置ID
     */
    @NotNull(message = "配置ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 类型（OUT出货/ IN补货）
     */
    private String type;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 商品类型(FU 福/ QIAN 签)
     */
    private String goodsType;

    /**
     * 数量
     */
    private Long count;

    /**
     * 操作时间
     */
    private Date operTime;


}
