package com.xu.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xu.biz.domain.BizPayRecord;
import com.xu.biz.domain.bo.BizPayRecordBo;
import com.xu.biz.domain.vo.BizPayRecordVo;
import com.xu.biz.mapper.BizPayRecordMapper;
import com.xu.biz.service.IBizPayRecordService;
import com.xu.common.core.utils.MapstructUtils;
import com.xu.common.core.utils.StringUtils;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 支付交易记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RequiredArgsConstructor
@Service
public class BizPayRecordServiceImpl implements IBizPayRecordService {

    private final BizPayRecordMapper baseMapper;

    /**
     * 查询支付交易记录
     *
     * @param id 主键
     * @return 支付交易记录
     */
    @Override
    public BizPayRecordVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询支付交易记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 支付交易记录分页列表
     */
    @Override
    public TableDataInfo<BizPayRecordVo> queryPageList(BizPayRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BizPayRecord> lqw = buildQueryWrapper(bo);
        Page<BizPayRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的支付交易记录列表
     *
     * @param bo 查询条件
     * @return 支付交易记录列表
     */
    @Override
    public List<BizPayRecordVo> queryList(BizPayRecordBo bo) {
        LambdaQueryWrapper<BizPayRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BizPayRecord> buildQueryWrapper(BizPayRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BizPayRecord> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(BizPayRecord::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getOutTradeNo()), BizPayRecord::getOutTradeNo, bo.getOutTradeNo());
        lqw.eq(bo.getDeviceId() != null, BizPayRecord::getDeviceId, bo.getDeviceId());
        lqw.eq(bo.getGoodsId() != null, BizPayRecord::getGoodsId, bo.getGoodsId());
        lqw.eq(bo.getPayConfigId() != null, BizPayRecord::getPayConfigId, bo.getPayConfigId());
        lqw.eq(bo.getMchConfigId() != null, BizPayRecord::getMchConfigId, bo.getMchConfigId());
        lqw.eq(StringUtils.isNotBlank(bo.getMchId()), BizPayRecord::getMchId, bo.getMchId());
        lqw.eq(StringUtils.isNotBlank(bo.getAppId()), BizPayRecord::getAppId, bo.getAppId());
        lqw.eq(bo.getChannel() != null, BizPayRecord::getChannel, bo.getChannel());
        lqw.eq(bo.getAmount() != null, BizPayRecord::getAmount, bo.getAmount());
        lqw.eq(bo.getActualAmount() != null, BizPayRecord::getActualAmount, bo.getActualAmount());
        lqw.eq(StringUtils.isNotBlank(bo.getCurrency()), BizPayRecord::getCurrency, bo.getCurrency());
        lqw.eq(StringUtils.isNotBlank(bo.getResultData()), BizPayRecord::getResultData, bo.getResultData());
        lqw.eq(bo.getStatus() != null, BizPayRecord::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getPayerUid()), BizPayRecord::getPayerUid, bo.getPayerUid());
        lqw.eq(StringUtils.isNotBlank(bo.getPayerAccount()), BizPayRecord::getPayerAccount, bo.getPayerAccount());
        lqw.eq(bo.getPayTime() != null, BizPayRecord::getPayTime, bo.getPayTime());
        return lqw;
    }

    /**
     * 新增支付交易记录
     *
     * @param bo 支付交易记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BizPayRecordBo bo) {
        BizPayRecord add = MapstructUtils.convert(bo, BizPayRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改支付交易记录
     *
     * @param bo 支付交易记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BizPayRecordBo bo) {
        BizPayRecord update = MapstructUtils.convert(bo, BizPayRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BizPayRecord entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除支付交易记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
