package com.xu.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xu.biz.domain.BizDeviceRemindRelation;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;



/**
 * 设备提醒关联视图对象 biz_device_remind_relation
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BizDeviceRemindRelation.class)
public class BizDeviceRemindRelationVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 设备ID
     */
    @ExcelProperty(value = "设备ID")
    private Long deviceId;

    /**
     * 提醒方式（SMS短信/EMAIL邮件）
     */
    @ExcelProperty(value = "提醒方式")
    private Long remindType;

    /**
     * 提醒内容
     */
    @ExcelProperty(value = "提醒内容")
    private Long remindContent;

    /**
     * 提醒人员
     */
    @ExcelProperty(value = "提醒人员")
    private String remindPerson;

    /**
     * 是否已经提醒
     */
    @ExcelProperty(value = "是否已经提醒")
    private Long reminded;


}
