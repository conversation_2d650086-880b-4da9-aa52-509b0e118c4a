package com.xu.biz.service;


import com.xu.biz.domain.bo.BizPlayMediaBo;
import com.xu.biz.domain.vo.BizPlayMediaVo;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 播放媒体Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IBizPlayMediaService {

    /**
     * 查询播放媒体
     *
     * @param id 主键
     * @return 播放媒体
     */
    BizPlayMediaVo queryById(Long id);

    /**
     * 分页查询播放媒体列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 播放媒体分页列表
     */
    TableDataInfo<BizPlayMediaVo> queryPageList(BizPlayMediaBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的播放媒体列表
     *
     * @param bo 查询条件
     * @return 播放媒体列表
     */
    List<BizPlayMediaVo> queryList(BizPlayMediaBo bo);

    /**
     * 新增播放媒体
     *
     * @param bo 播放媒体
     * @return 是否新增成功
     */
    Boolean insertByBo(BizPlayMediaBo bo);

    /**
     * 修改播放媒体
     *
     * @param bo 播放媒体
     * @return 是否修改成功
     */
    Boolean updateByBo(BizPlayMediaBo bo);

    /**
     * 校验并批量删除播放媒体信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
