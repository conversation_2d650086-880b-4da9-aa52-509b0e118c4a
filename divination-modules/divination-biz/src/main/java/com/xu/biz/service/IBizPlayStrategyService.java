package com.xu.biz.service;


import com.xu.biz.domain.bo.BizPlayStrategyBo;
import com.xu.biz.domain.vo.BizPlayStrategyVo;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 播放策略Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IBizPlayStrategyService {

    /**
     * 查询播放策略
     *
     * @param id 主键
     * @return 播放策略
     */
    BizPlayStrategyVo queryById(Long id);

    /**
     * 分页查询播放策略列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 播放策略分页列表
     */
    TableDataInfo<BizPlayStrategyVo> queryPageList(BizPlayStrategyBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的播放策略列表
     *
     * @param bo 查询条件
     * @return 播放策略列表
     */
    List<BizPlayStrategyVo> queryList(BizPlayStrategyBo bo);

    /**
     * 新增播放策略
     *
     * @param bo 播放策略
     * @return 是否新增成功
     */
    Boolean insertByBo(BizPlayStrategyBo bo);

    /**
     * 修改播放策略
     *
     * @param bo 播放策略
     * @return 是否修改成功
     */
    Boolean updateByBo(BizPlayStrategyBo bo);

    /**
     * 校验并批量删除播放策略信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
