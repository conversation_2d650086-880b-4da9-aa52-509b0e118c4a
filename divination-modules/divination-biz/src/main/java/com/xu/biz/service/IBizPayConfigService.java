package com.xu.biz.service;


import com.xu.biz.domain.bo.BizPayConfigBo;
import com.xu.biz.domain.vo.BizPayConfigVo;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 支付配置Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IBizPayConfigService {

    /**
     * 查询支付配置
     *
     * @param id 主键
     * @return 支付配置
     */
    BizPayConfigVo queryById(Long id);

    /**
     * 分页查询支付配置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 支付配置分页列表
     */
    TableDataInfo<BizPayConfigVo> queryPageList(BizPayConfigBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的支付配置列表
     *
     * @param bo 查询条件
     * @return 支付配置列表
     */
    List<BizPayConfigVo> queryList(BizPayConfigBo bo);

    /**
     * 新增支付配置
     *
     * @param bo 支付配置
     * @return 是否新增成功
     */
    Boolean insertByBo(BizPayConfigBo bo);

    /**
     * 修改支付配置
     *
     * @param bo 支付配置
     * @return 是否修改成功
     */
    Boolean updateByBo(BizPayConfigBo bo);

    /**
     * 校验并批量删除支付配置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
