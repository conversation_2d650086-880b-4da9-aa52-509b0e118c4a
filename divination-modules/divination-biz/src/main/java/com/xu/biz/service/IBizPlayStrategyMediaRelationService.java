package com.xu.biz.service;


import com.xu.biz.domain.bo.BizPlayStrategyMediaRelationBo;
import com.xu.biz.domain.vo.BizPlayStrategyMediaRelationVo;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 播放策略媒体关联Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IBizPlayStrategyMediaRelationService {

    /**
     * 查询播放策略媒体关联
     *
     * @param strategyId 主键
     * @return 播放策略媒体关联
     */
    BizPlayStrategyMediaRelationVo queryById(Long strategyId);

    /**
     * 分页查询播放策略媒体关联列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 播放策略媒体关联分页列表
     */
    TableDataInfo<BizPlayStrategyMediaRelationVo> queryPageList(BizPlayStrategyMediaRelationBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的播放策略媒体关联列表
     *
     * @param bo 查询条件
     * @return 播放策略媒体关联列表
     */
    List<BizPlayStrategyMediaRelationVo> queryList(BizPlayStrategyMediaRelationBo bo);

    /**
     * 新增播放策略媒体关联
     *
     * @param bo 播放策略媒体关联
     * @return 是否新增成功
     */
    Boolean insertByBo(BizPlayStrategyMediaRelationBo bo);

    /**
     * 修改播放策略媒体关联
     *
     * @param bo 播放策略媒体关联
     * @return 是否修改成功
     */
    Boolean updateByBo(BizPlayStrategyMediaRelationBo bo);

    /**
     * 校验并批量删除播放策略媒体关联信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
