package com.xu.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xu.biz.domain.BizAppUpgradePackage;
import com.xu.common.excel.annotation.ExcelDictFormat;
import com.xu.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;



/**
 * 安装包管理视图对象 biz_app_upgrade_package
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BizAppUpgradePackage.class)
public class BizAppUpgradePackageVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 安装包ID
     */
    private Long id;

    /**
     * 安装包名称
     */
    @ExcelProperty(value = "安装包名称")
    private String name;

    /**
     * 版本号(整数)
     */
    @ExcelProperty(value = "数字版本号")
    private Long code;

    /**
     * 版本编号(如1.0.0)
     */
    @ExcelProperty(value = "版本编号")
    private String number;

    /**
     * 文件ID
     */
    private String fileId;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 版本说明
     */
    @ExcelProperty(value = "版本说明")
    private String releaseNotes;

    /**
     * 是否强制更新(0-否 1-是)
     */
    @ExcelDictFormat(readConverterExp = "0=否,1=是")
    @ExcelProperty(value = "是否强制更新",converter = ExcelDictConvert.class)
    private Long forceUpdate;


}
