package com.xu.biz.service;


import com.xu.biz.domain.bo.BizPayMchConfigBo;
import com.xu.biz.domain.vo.BizPayMchConfigVo;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 支付商户管理Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IBizPayMchConfigService {

    /**
     * 查询支付商户管理
     *
     * @param id 主键
     * @return 支付商户管理
     */
    BizPayMchConfigVo queryById(Long id);

    /**
     * 分页查询支付商户管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 支付商户管理分页列表
     */
    TableDataInfo<BizPayMchConfigVo> queryPageList(BizPayMchConfigBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的支付商户管理列表
     *
     * @param bo 查询条件
     * @return 支付商户管理列表
     */
    List<BizPayMchConfigVo> queryList(BizPayMchConfigBo bo);

    /**
     * 新增支付商户管理
     *
     * @param bo 支付商户管理
     * @return 是否新增成功
     */
    Boolean insertByBo(BizPayMchConfigBo bo);

    /**
     * 修改支付商户管理
     *
     * @param bo 支付商户管理
     * @return 是否修改成功
     */
    Boolean updateByBo(BizPayMchConfigBo bo);

    /**
     * 校验并批量删除支付商户管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
