package com.xu.biz.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xu.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 支付交易记录对象 biz_pay_record
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_pay_record")
public class BizPayRecord extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 支付记录ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 商户订单号
     */
    private String outTradeNo;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 支付配置ID
     */
    private Long payConfigId;

    /**
     * 商户配置ID
     */
    private Long mchConfigId;

    /**
     * 商户ID
     */
    private String mchId;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 支付渠道(1-支付宝 2-微信)
     */
    private Long channel;

    /**
     * 订单金额(元)
     */
    private Long amount;

    /**
     * 实收金额(元)
     */
    private Long actualAmount;

    /**
     * 货币类型
     */
    private String currency;

    /**
     * 返回数据
     */
    private String resultData;

    /**
     * 订单描述
     */
    private String remark;

    /**
     * 状态(0-待支付 1-支付成功 2-已关闭 3-已退款)
     */
    private Integer status;

    /**
     * 付款方用户标识
     */
    private String payerUid;

    /**
     * 付款方账号
     */
    private String payerAccount;

    /**
     * 支付成功时间
     */
    private Date payTime;

    /**
     * 删除标识 0否 1是
     */
    @TableLogic
    private Long delFlag;


}
