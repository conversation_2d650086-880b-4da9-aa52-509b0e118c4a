package com.xu.biz.controller.web;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.xu.biz.domain.bo.BizDeviceTypeBo;
import com.xu.biz.domain.vo.BizDeviceTypeVo;
import com.xu.biz.service.IBizDeviceTypeService;
import com.xu.common.core.domain.R;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.excel.utils.ExcelUtil;
import com.xu.common.idempotent.annotation.RepeatSubmit;
import com.xu.common.log.annotation.Log;
import com.xu.common.log.enums.BusinessType;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import com.xu.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 设备类型
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/biz/deviceType")
public class BizDeviceTypeController extends BaseController {

    private final IBizDeviceTypeService bizDeviceTypeService;

    /**
     * 查询设备类型列表
     */
    @SaCheckPermission("biz:deviceType:list")
    @GetMapping("/list")
    public TableDataInfo<BizDeviceTypeVo> list(BizDeviceTypeBo bo, PageQuery pageQuery) {
        return bizDeviceTypeService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询设备类型列表（不分页）
     */
    @SaCheckPermission("biz:deviceType:list")
    @GetMapping("/listAll")
    public R<List<BizDeviceTypeVo>> listAll(BizDeviceTypeBo bo) {
        List<BizDeviceTypeVo> list = bizDeviceTypeService.queryList(bo);
        return R.ok(list);
    }

    /**
     * 导出设备类型列表
     */
    @SaCheckPermission("biz:deviceType:export")
    @Log(title = "设备类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BizDeviceTypeBo bo, HttpServletResponse response) {
        List<BizDeviceTypeVo> list = bizDeviceTypeService.queryList(bo);
        ExcelUtil.exportExcel(list, "设备类型", BizDeviceTypeVo.class, response);
    }

    /**
     * 获取设备类型详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:deviceType:query")
    @GetMapping("/{id}")
    public R<BizDeviceTypeVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(bizDeviceTypeService.queryById(id));
    }

    /**
     * 新增设备类型
     */
    @SaCheckPermission("biz:deviceType:add")
    @Log(title = "设备类型", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BizDeviceTypeBo bo) {
        return toAjax(bizDeviceTypeService.insertByBo(bo));
    }

    /**
     * 修改设备类型
     */
    @SaCheckPermission("biz:deviceType:edit")
    @Log(title = "设备类型", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BizDeviceTypeBo bo) {
        return toAjax(bizDeviceTypeService.updateByBo(bo));
    }

    /**
     * 删除设备类型
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:deviceType:remove")
    @Log(title = "设备类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(bizDeviceTypeService.deleteWithValidByIds(List.of(ids), true));
    }
}
