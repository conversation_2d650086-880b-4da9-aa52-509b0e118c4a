package com.xu.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xu.biz.domain.BizSpace;
import com.xu.biz.domain.bo.BizSpaceBo;
import com.xu.biz.domain.vo.BizSpaceVo;
import com.xu.biz.mapper.BizSpaceMapper;
import com.xu.biz.service.IBizSpaceService;
import com.xu.common.core.utils.MapstructUtils;
import com.xu.common.core.utils.StringUtils;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 空间管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RequiredArgsConstructor
@Service
public class BizSpaceServiceImpl implements IBizSpaceService {

    private final BizSpaceMapper baseMapper;

    /**
     * 查询空间管理
     *
     * @param id 主键
     * @return 空间管理
     */
    @Override
    public BizSpaceVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询空间管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 空间管理分页列表
     */
    @Override
    public TableDataInfo<BizSpaceVo> queryPageList(BizSpaceBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BizSpace> lqw = buildQueryWrapper(bo);
        Page<BizSpaceVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的空间管理列表
     *
     * @param bo 查询条件
     * @return 空间管理列表
     */
    @Override
    public List<BizSpaceVo> queryList(BizSpaceBo bo) {
        LambdaQueryWrapper<BizSpace> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BizSpace> buildQueryWrapper(BizSpaceBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BizSpace> lqw = Wrappers.lambdaQuery();
        // 修改排序规则：优先按排序值升序，其次按创建时间降序
        lqw.orderByAsc(BizSpace::getSort)
           .orderByDesc(BizSpace::getCreateTime);
        lqw.like(StringUtils.isNotBlank(bo.getName()), BizSpace::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getCode()), BizSpace::getCode, bo.getCode());
        lqw.eq(StringUtils.isNotBlank(bo.getX()), BizSpace::getX, bo.getX());
        lqw.eq(StringUtils.isNotBlank(bo.getY()), BizSpace::getY, bo.getY());
        lqw.eq(bo.getSort() != null, BizSpace::getSort, bo.getSort());
        lqw.eq(bo.getParentId() != null, BizSpace::getParentId, bo.getParentId());
        return lqw;
    }

    /**
     * 新增空间管理
     *
     * @param bo 空间管理
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BizSpaceBo bo) {
        BizSpace add = MapstructUtils.convert(bo, BizSpace.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改空间管理
     *
     * @param bo 空间管理
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BizSpaceBo bo) {
        BizSpace update = MapstructUtils.convert(bo, BizSpace.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BizSpace entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除空间管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
