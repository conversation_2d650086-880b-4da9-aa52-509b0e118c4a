package com.xu.biz.service;

import com.xu.biz.domain.bo.BizSpaceBo;
import com.xu.biz.domain.vo.BizSpaceVo;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 空间管理Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IBizSpaceService {

    /**
     * 查询空间管理
     *
     * @param id 主键
     * @return 空间管理
     */
    BizSpaceVo queryById(Long id);

    /**
     * 分页查询空间管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 空间管理分页列表
     */
    TableDataInfo<BizSpaceVo> queryPageList(BizSpaceBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的空间管理列表
     *
     * @param bo 查询条件
     * @return 空间管理列表
     */
    List<BizSpaceVo> queryList(BizSpaceBo bo);

    /**
     * 新增空间管理
     *
     * @param bo 空间管理
     * @return 是否新增成功
     */
    Boolean insertByBo(BizSpaceBo bo);

    /**
     * 修改空间管理
     *
     * @param bo 空间管理
     * @return 是否修改成功
     */
    Boolean updateByBo(BizSpaceBo bo);

    /**
     * 校验并批量删除空间管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
