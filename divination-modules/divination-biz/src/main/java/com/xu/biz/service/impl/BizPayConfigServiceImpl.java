package com.xu.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xu.biz.domain.BizPayConfig;
import com.xu.biz.domain.bo.BizPayConfigBo;
import com.xu.biz.domain.vo.BizPayConfigVo;
import com.xu.biz.mapper.BizPayConfigMapper;
import com.xu.biz.service.IBizPayConfigService;
import com.xu.common.core.utils.MapstructUtils;
import com.xu.common.core.utils.StringUtils;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 支付配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RequiredArgsConstructor
@Service
public class BizPayConfigServiceImpl implements IBizPayConfigService {

    private final BizPayConfigMapper baseMapper;

    /**
     * 查询支付配置
     *
     * @param id 主键
     * @return 支付配置
     */
    @Override
    public BizPayConfigVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询支付配置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 支付配置分页列表
     */
    @Override
    public TableDataInfo<BizPayConfigVo> queryPageList(BizPayConfigBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BizPayConfig> lqw = buildQueryWrapper(bo);
        Page<BizPayConfigVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的支付配置列表
     *
     * @param bo 查询条件
     * @return 支付配置列表
     */
    @Override
    public List<BizPayConfigVo> queryList(BizPayConfigBo bo) {
        LambdaQueryWrapper<BizPayConfig> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BizPayConfig> buildQueryWrapper(BizPayConfigBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BizPayConfig> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(BizPayConfig::getId);
        lqw.like(StringUtils.isNotBlank(bo.getConfigName()), BizPayConfig::getConfigName, bo.getConfigName());
        lqw.eq(StringUtils.isNotBlank(bo.getPayType()), BizPayConfig::getPayType, bo.getPayType());
        lqw.eq(StringUtils.isNotBlank(bo.getAppId()), BizPayConfig::getAppId, bo.getAppId());
        lqw.eq(StringUtils.isNotBlank(bo.getMchId()), BizPayConfig::getMchId, bo.getMchId());
        lqw.eq(StringUtils.isNotBlank(bo.getDomain()), BizPayConfig::getDomain, bo.getDomain());
        lqw.eq(StringUtils.isNotBlank(bo.getNotifyUrl()), BizPayConfig::getNotifyUrl, bo.getNotifyUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getAliPrivateKey()), BizPayConfig::getAliPrivateKey, bo.getAliPrivateKey());
        lqw.eq(StringUtils.isNotBlank(bo.getAliPublicKey()), BizPayConfig::getAliPublicKey, bo.getAliPublicKey());
        lqw.eq(StringUtils.isNotBlank(bo.getAliAppCertPath()), BizPayConfig::getAliAppCertPath, bo.getAliAppCertPath());
        lqw.eq(StringUtils.isNotBlank(bo.getAliPayCertPath()), BizPayConfig::getAliPayCertPath, bo.getAliPayCertPath());
        lqw.eq(StringUtils.isNotBlank(bo.getAliPayRootCertPath()), BizPayConfig::getAliPayRootCertPath, bo.getAliPayRootCertPath());
        lqw.eq(StringUtils.isNotBlank(bo.getAliServiceUrl()), BizPayConfig::getAliServiceUrl, bo.getAliServiceUrl());
        lqw.eq(bo.getAliCertModel() != null, BizPayConfig::getAliCertModel, bo.getAliCertModel());
        lqw.eq(StringUtils.isNotBlank(bo.getWxApiKeyV2()), BizPayConfig::getWxApiKeyV2, bo.getWxApiKeyV2());
        lqw.eq(StringUtils.isNotBlank(bo.getWxApiKeyV3()), BizPayConfig::getWxApiKeyV3, bo.getWxApiKeyV3());
        lqw.eq(StringUtils.isNotBlank(bo.getWxCertPath()), BizPayConfig::getWxCertPath, bo.getWxCertPath());
        lqw.eq(StringUtils.isNotBlank(bo.getWxCertP12Path()), BizPayConfig::getWxCertP12Path, bo.getWxCertP12Path());
        lqw.eq(StringUtils.isNotBlank(bo.getWxKeyPath()), BizPayConfig::getWxKeyPath, bo.getWxKeyPath());
        lqw.eq(StringUtils.isNotBlank(bo.getWxPlatformCertPath()), BizPayConfig::getWxPlatformCertPath, bo.getWxPlatformCertPath());
        lqw.eq(bo.getStatus() != null, BizPayConfig::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增支付配置
     *
     * @param bo 支付配置
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BizPayConfigBo bo) {
        BizPayConfig add = MapstructUtils.convert(bo, BizPayConfig.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改支付配置
     *
     * @param bo 支付配置
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BizPayConfigBo bo) {
        BizPayConfig update = MapstructUtils.convert(bo, BizPayConfig.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BizPayConfig entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除支付配置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
