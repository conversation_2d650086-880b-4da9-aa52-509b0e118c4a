package com.xu.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xu.biz.domain.BizPlayMedia;
import com.xu.common.excel.annotation.ExcelDictFormat;
import com.xu.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 播放媒体视图对象 biz_play_media
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BizPlayMedia.class)
public class BizPlayMediaVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String name;

    /**
     * 节假日类型（字典code）
     */
    @ExcelDictFormat(dictType = "holiday_type")
    @ExcelProperty(value = "节假日类型", converter = ExcelDictConvert.class)
    private String holidayType;

    /**
     * 播放类型
     */
    @ExcelDictFormat(dictType = "play_type")
    @ExcelProperty(value = "播放类型", converter = ExcelDictConvert.class)
    private String playStyle;

    /**
     * 描述
     */
    @ExcelProperty(value = "描述")
    private String remark;


}
