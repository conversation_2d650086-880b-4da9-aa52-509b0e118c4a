package com.xu.biz.domain.bo;

import com.xu.biz.domain.BizDeviceType;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备类型业务对象 biz_device_type
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BizDeviceType.class, reverseConvertGenerate = false)
public class BizDeviceTypeBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 类型名称
     */
    @NotBlank(message = "类型名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 类型描述
     */
    private String remark;


}
