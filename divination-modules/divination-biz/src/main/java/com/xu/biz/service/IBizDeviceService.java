package com.xu.biz.service;


import com.xu.biz.domain.bo.BizDeviceBo;
import com.xu.biz.domain.vo.BizDeviceVo;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 设备基础信息Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IBizDeviceService {

    /**
     * 查询设备基础信息
     *
     * @param id 主键
     * @return 设备基础信息
     */
    BizDeviceVo queryById(Long id);

    /**
     * 分页查询设备基础信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 设备基础信息分页列表
     */
    TableDataInfo<BizDeviceVo> queryPageList(BizDeviceBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的设备基础信息列表
     *
     * @param bo 查询条件
     * @return 设备基础信息列表
     */
    List<BizDeviceVo> queryList(BizDeviceBo bo);

    /**
     * 新增设备基础信息
     *
     * @param bo 设备基础信息
     * @return 是否新增成功
     */
    Boolean insertByBo(BizDeviceBo bo);

    /**
     * 修改设备基础信息
     *
     * @param bo 设备基础信息
     * @return 是否修改成功
     */
    Boolean updateByBo(BizDeviceBo bo);

    /**
     * 校验并批量删除设备基础信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 播放配置
     *
     * @param bo
     * @return
     */
    Boolean playStrategy(BizDeviceBo bo);

    /**
     * 设备重启配置
     *
     * @param bo
     * @return
     */
    Boolean restart(BizDeviceBo bo);

    /**
     * 强制出签
     *
     * @param bo
     * @return
     */
    Boolean forcedShipments(BizDeviceBo bo);
}
