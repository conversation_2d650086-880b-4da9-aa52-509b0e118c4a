package com.xu.biz.controller.web;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.xu.biz.domain.bo.BizGoodsBo;
import com.xu.biz.domain.vo.BizGoodsVo;
import com.xu.biz.service.IBizGoodsService;
import com.xu.common.core.domain.R;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.excel.utils.ExcelUtil;
import com.xu.common.idempotent.annotation.RepeatSubmit;
import com.xu.common.log.annotation.Log;
import com.xu.common.log.enums.BusinessType;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import com.xu.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 商品
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/biz/goods")
public class BizGoodsController extends BaseController {

    private final IBizGoodsService bizGoodsService;

    /**
     * 查询商品列表
     */
    @SaCheckPermission("biz:goods:list")
    @GetMapping("/list")
    public TableDataInfo<BizGoodsVo> list(BizGoodsBo bo, PageQuery pageQuery) {
        return bizGoodsService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出商品列表
     */
    @SaCheckPermission("biz:goods:export")
    @Log(title = "商品", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BizGoodsBo bo, HttpServletResponse response) {
        List<BizGoodsVo> list = bizGoodsService.queryList(bo);
        ExcelUtil.exportExcel(list, "商品", BizGoodsVo.class, response);
    }

    /**
     * 获取商品详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:goods:query")
    @GetMapping("/{id}")
    public R<BizGoodsVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(bizGoodsService.queryById(id));
    }

    /**
     * 新增商品
     */
    @SaCheckPermission("biz:goods:add")
    @Log(title = "商品", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BizGoodsBo bo) {
        return toAjax(bizGoodsService.insertByBo(bo));
    }

    /**
     * 修改商品
     */
    @SaCheckPermission("biz:goods:edit")
    @Log(title = "商品", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BizGoodsBo bo) {
        return toAjax(bizGoodsService.updateByBo(bo));
    }

    /**
     * 删除商品
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:goods:remove")
    @Log(title = "商品", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(bizGoodsService.deleteWithValidByIds(List.of(ids), true));
    }
}
