package com.xu.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xu.biz.domain.BizContractRentRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 合同租金记录视图对象 biz_contract_rent_record
 *
 * <AUTHOR>
 * @date 2025-05-23
 * @deprecated 此类已废弃，请使用新的实现
 */
@Deprecated
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BizContractRentRecord.class)
public class BizContractRentRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    private Long id;

    /**
     * 合同ID
     */
    @ExcelProperty(value = "合同ID")
    private Long contractId;

    /**
     * 缴纳人员
     */
    @ExcelProperty(value = "缴纳人员")
    private String payPerson;

    /**
     * 缴纳时间
     */
    @ExcelProperty(value = "缴纳时间")
    private Date payTime;

    /**
     * 缴纳金额
     */
    @ExcelProperty(value = "缴纳金额")
    private Long payAmount;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private Date remark;


}
