package com.xu.biz.service;


import com.xu.biz.domain.bo.BizDeviceGroupBo;
import com.xu.biz.domain.vo.BizDeviceGroupVo;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 设备分组Service接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface IBizDeviceGroupService {

    /**
     * 查询设备分组
     *
     * @param id 主键
     * @return 设备分组
     */
    BizDeviceGroupVo queryById(Long id);

    /**
     * 分页查询设备分组列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 设备分组分页列表
     */
    TableDataInfo<BizDeviceGroupVo> queryPageList(BizDeviceGroupBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的设备分组列表
     *
     * @param bo 查询条件
     * @return 设备分组列表
     */
    List<BizDeviceGroupVo> queryList(BizDeviceGroupBo bo);

    /**
     * 新增设备分组
     *
     * @param bo 设备分组
     * @return 是否新增成功
     */
    Boolean insertByBo(BizDeviceGroupBo bo);

    /**
     * 修改设备分组
     *
     * @param bo 设备分组
     * @return 是否修改成功
     */
    Boolean updateByBo(BizDeviceGroupBo bo);

    /**
     * 校验并批量删除设备分组信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
