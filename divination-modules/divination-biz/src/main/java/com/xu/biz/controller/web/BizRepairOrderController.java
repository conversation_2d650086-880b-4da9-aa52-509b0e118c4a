package com.xu.biz.controller.web;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.xu.biz.domain.bo.BizRepairOrderBo;
import com.xu.biz.domain.vo.BizRepairOrderVo;
import com.xu.biz.service.IBizRepairOrderService;
import com.xu.common.core.domain.R;
import com.xu.common.core.validate.AddGroup;
import com.xu.common.core.validate.EditGroup;
import com.xu.common.excel.utils.ExcelUtil;
import com.xu.common.idempotent.annotation.RepeatSubmit;
import com.xu.common.log.annotation.Log;
import com.xu.common.log.enums.BusinessType;
import com.xu.common.mybatis.core.page.PageQuery;
import com.xu.common.mybatis.core.page.TableDataInfo;
import com.xu.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 维修工单
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/web/biz/repairOrder")
public class BizRepairOrderController extends BaseController {

    private final IBizRepairOrderService bizRepairOrderService;

    /**
     * 查询维修工单列表
     */
    @SaCheckPermission("biz:repairOrder:list")
    @GetMapping("/list")
    public TableDataInfo<BizRepairOrderVo> list(BizRepairOrderBo bo, PageQuery pageQuery) {
        return bizRepairOrderService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出维修工单列表
     */
    @SaCheckPermission("biz:repairOrder:export")
    @Log(title = "维修工单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BizRepairOrderBo bo, HttpServletResponse response) {
        List<BizRepairOrderVo> list = bizRepairOrderService.queryList(bo);
        ExcelUtil.exportExcel(list, "维修工单", BizRepairOrderVo.class, response);
    }

    /**
     * 获取维修工单详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:repairOrder:query")
    @GetMapping("/{id}")
    public R<BizRepairOrderVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(bizRepairOrderService.queryById(id));
    }

    /**
     * 新增维修工单
     */
    @SaCheckPermission("biz:repairOrder:add")
    @Log(title = "维修工单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BizRepairOrderBo bo) {
        bo.setOrderNo("NO" + System.currentTimeMillis());
        return toAjax(bizRepairOrderService.insertByBo(bo));
    }

    /**
     * 修改维修工单
     */
    @SaCheckPermission("biz:repairOrder:edit")
    @Log(title = "维修工单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BizRepairOrderBo bo) {
        return toAjax(bizRepairOrderService.updateByBo(bo));
    }

    /**
     * 删除维修工单
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:repairOrder:remove")
    @Log(title = "维修工单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(bizRepairOrderService.deleteWithValidByIds(List.of(ids), true));
    }
}
