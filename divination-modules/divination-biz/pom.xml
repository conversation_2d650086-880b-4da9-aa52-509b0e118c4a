<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xu</groupId>
        <artifactId>divination-modules</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>divination-biz</artifactId>
    <description> 抽奖机主业务 </description>

    <dependencies>
        <!-- 通用工具-->
        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-translation</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-log</artifactId>
        </dependency>

        <!-- excel-->
        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-excel</artifactId>
        </dependency>

        <!-- SMS功能模块 -->
        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-sms</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-tenant</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-idempotent</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-sensitive</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-encrypt</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-websocket</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-sse</artifactId>
        </dependency>

        <!-- mqtt -->
        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-mqtt</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-system</artifactId>
        </dependency>
    </dependencies>

</project>
