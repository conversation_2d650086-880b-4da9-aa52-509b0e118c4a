<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xu</groupId>
        <artifactId>divination-common</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>divination-common-mqtt</artifactId>
    <description> divination-common-web mqtt服务 </description>

    <dependencies>
        <dependency>
            <groupId>com.xu</groupId>
            <artifactId>divination-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>net.dreamlu</groupId>
            <artifactId>mica-mqtt-client-spring-boot-starter</artifactId>
            <version>2.3.9</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>
    </dependencies>
</project>
