package com.xu.common.mqtt;

import lombok.RequiredArgsConstructor;
import net.dreamlu.iot.mqtt.spring.client.MqttClientTemplate;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/06/20
 */
@Component
@RequiredArgsConstructor
public class MqttPublish {

    private MqttClientTemplate mqttClientTemplate;

    public void publish(String topic, Object payload) {
        mqttClientTemplate.publish(topic, payload);
    }

}
